"use client"

import type React from "react"

import { useState, useRef } from "react"
import { Upload, Music, ImageIcon, X, CheckCircle, AlertCircle, Loader2 } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

interface UploadFormProps {
  userTier: 0 | 1 | 2
  onUpload: (formData: FormData) => Promise<void>
}

interface UploadFile {
  file: File
  preview?: string
  status: "pending" | "uploading" | "analyzing" | "complete" | "error"
  progress: number
  analysis?: {
    tempo: number
    tone: string
    duration: number
  }
}

const genres = [
  "Pop",
  "Rock",
  "Hip Hop",
  "Electronic",
  "Jazz",
  "Classical",
  "Country",
  "R&B",
  "Reggae",
  "Blues",
  "Folk",
  "Punk",
  "Metal",
  "Indie",
  "Alternative",
]

const moods = [
  "Happy",
  "Sad",
  "Energetic",
  "Calm",
  "Romantic",
  "Dark",
  "Uplifting",
  "Melancholic",
  "Aggressive",
  "Peaceful",
  "Nostalgic",
  "Mysterious",
]

const tiers = [
  { value: 0, label: "Free", description: "Available to all users" },
  { value: 1, label: "Pro", description: "Pro subscribers only" },
  { value: 2, label: "Business", description: "Business subscribers only" },
]

export function UploadForm({ userTier, onUpload }: UploadFormProps) {
  const [audioFile, setAudioFile] = useState<UploadFile | null>(null)
  const [coverFile, setCoverFile] = useState<UploadFile | null>(null)
  const [formData, setFormData] = useState({
    title: "",
    artist: "",
    album: "",
    genre: "",
    mood: "",
    tier: 0,
    description: "",
  })
  const [isUploading, setIsUploading] = useState(false)

  const audioInputRef = useRef<HTMLInputElement>(null)
  const coverInputRef = useRef<HTMLInputElement>(null)

  const getUploadLimits = () => {
    switch (userTier) {
      case 1:
        return { maxFiles: 50, maxSize: "100MB" }
      case 2:
        return { maxFiles: 200, maxSize: "500MB" }
      default:
        return { maxFiles: 5, maxSize: "10MB" }
    }
  }

  const handleAudioUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith("audio/")) {
      alert("Please select a valid audio file")
      return
    }

    // Validate file size based on tier
    const limits = getUploadLimits()
    const maxSizeBytes = userTier === 0 ? 10 * 1024 * 1024 : userTier === 1 ? 100 * 1024 * 1024 : 500 * 1024 * 1024

    if (file.size > maxSizeBytes) {
      alert(`File size exceeds ${limits.maxSize} limit for your tier`)
      return
    }

    setAudioFile({
      file,
      status: "pending",
      progress: 0,
    })
  }

  const handleCoverUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith("image/")) {
      alert("Please select a valid image file")
      return
    }

    const preview = URL.createObjectURL(file)
    setCoverFile({
      file,
      preview,
      status: "pending",
      progress: 0,
    })
  }

  const analyzeAudio = async (file: File): Promise<{ tempo: number; tone: string; duration: number }> => {
    // Simulate audio analysis with aubio
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          tempo: Math.floor(Math.random() * 60) + 80, // 80-140 BPM
          tone: ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"][Math.floor(Math.random() * 12)],
          duration: Math.floor(Math.random() * 240) + 60, // 1-5 minutes
        })
      }, 2000)
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!audioFile || !formData.title || !formData.artist) {
      alert("Please fill in all required fields")
      return
    }

    setIsUploading(true)

    try {
      // Update audio file status
      setAudioFile((prev) => (prev ? { ...prev, status: "analyzing" } : null))

      // Analyze audio
      const analysis = await analyzeAudio(audioFile.file)
      setAudioFile((prev) => (prev ? { ...prev, analysis, status: "uploading" } : null))

      // Create form data
      const uploadData = new FormData()
      uploadData.append("audio", audioFile.file)
      if (coverFile) {
        uploadData.append("cover", coverFile.file)
      }

      // Add metadata
      Object.entries(formData).forEach(([key, value]) => {
        uploadData.append(key, value.toString())
      })

      // Add analysis results
      uploadData.append("tempo", analysis.tempo.toString())
      uploadData.append("tone", analysis.tone)
      uploadData.append("duration", analysis.duration.toString())

      // Simulate upload progress
      for (let i = 0; i <= 100; i += 10) {
        setAudioFile((prev) => (prev ? { ...prev, progress: i } : null))
        await new Promise((resolve) => setTimeout(resolve, 200))
      }

      await onUpload(uploadData)

      setAudioFile((prev) => (prev ? { ...prev, status: "complete" } : null))

      // Reset form
      setTimeout(() => {
        setAudioFile(null)
        setCoverFile(null)
        setFormData({
          title: "",
          artist: "",
          album: "",
          genre: "",
          mood: "",
          tier: 0,
          description: "",
        })
      }, 2000)
    } catch (error) {
      console.error("Upload failed:", error)
      setAudioFile((prev) => (prev ? { ...prev, status: "error" } : null))
    } finally {
      setIsUploading(false)
    }
  }

  const limits = getUploadLimits()

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Upload Limits Info */}
      <Card className="bg-gray-900 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">Upload Limits</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-400">Your Plan:</span>
            <Badge
              className={userTier === 0 ? "bg-gray-500" : userTier === 1 ? "bg-yellow-500 text-black" : "bg-purple-500"}
            >
              {userTier === 0 ? "Free" : userTier === 1 ? "Pro" : "Business"}
            </Badge>
          </div>
          <div className="flex items-center justify-between text-sm mt-2">
            <span className="text-gray-400">Max files per month:</span>
            <span className="text-white">{limits.maxFiles}</span>
          </div>
          <div className="flex items-center justify-between text-sm mt-2">
            <span className="text-gray-400">Max file size:</span>
            <span className="text-white">{limits.maxSize}</span>
          </div>
        </CardContent>
      </Card>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Audio Upload */}
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Audio File</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {!audioFile ? (
              <div
                onClick={() => audioInputRef.current?.click()}
                className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-green-500 transition-colors"
              >
                <Music className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-white font-medium">Click to upload audio file</p>
                <p className="text-gray-400 text-sm">MP3, WAV up to {limits.maxSize}</p>
              </div>
            ) : (
              <div className="border border-gray-600 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-3">
                    <Music className="h-8 w-8 text-green-500" />
                    <div>
                      <p className="text-white font-medium">{audioFile.file.name}</p>
                      <p className="text-gray-400 text-sm">{(audioFile.file.size / (1024 * 1024)).toFixed(2)} MB</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {audioFile.status === "analyzing" && <Loader2 className="h-5 w-5 animate-spin text-blue-500" />}
                    {audioFile.status === "complete" && <CheckCircle className="h-5 w-5 text-green-500" />}
                    {audioFile.status === "error" && <AlertCircle className="h-5 w-5 text-red-500" />}
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => setAudioFile(null)}
                      className="h-8 w-8 text-gray-400 hover:text-white"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {audioFile.status === "uploading" && <Progress value={audioFile.progress} className="mb-2" />}

                {audioFile.analysis && (
                  <div className="flex gap-2 mt-2">
                    <Badge variant="outline">{audioFile.analysis.tempo} BPM</Badge>
                    <Badge variant="outline">{audioFile.analysis.tone}</Badge>
                    <Badge variant="outline">
                      {Math.floor(audioFile.analysis.duration / 60)}:
                      {(audioFile.analysis.duration % 60).toString().padStart(2, "0")}
                    </Badge>
                  </div>
                )}
              </div>
            )}
            <input ref={audioInputRef} type="file" accept="audio/*" onChange={handleAudioUpload} className="hidden" />
          </CardContent>
        </Card>

        {/* Cover Image Upload */}
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Cover Image (Optional)</CardTitle>
          </CardHeader>
          <CardContent>
            {!coverFile ? (
              <div
                onClick={() => coverInputRef.current?.click()}
                className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-green-500 transition-colors"
              >
                <ImageIcon className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-white font-medium">Click to upload cover image</p>
                <p className="text-gray-400 text-sm">JPG, PNG up to 5MB</p>
              </div>
            ) : (
              <div className="border border-gray-600 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="h-16 w-16 rounded-lg overflow-hidden bg-gray-800">
                    <img
                      src={coverFile.preview || "/placeholder.svg"}
                      alt="Cover preview"
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <p className="text-white font-medium">{coverFile.file.name}</p>
                    <p className="text-gray-400 text-sm">{(coverFile.file.size / (1024 * 1024)).toFixed(2)} MB</p>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => setCoverFile(null)}
                    className="h-8 w-8 text-gray-400 hover:text-white"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
            <input ref={coverInputRef} type="file" accept="image/*" onChange={handleCoverUpload} className="hidden" />
          </CardContent>
        </Card>

        {/* Metadata */}
        <Card className="bg-gray-900 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Track Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="title" className="text-white">
                  Title *
                </Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
                  className="bg-gray-800 border-gray-600 text-white"
                  placeholder="Enter track title"
                  required
                />
              </div>
              <div>
                <Label htmlFor="artist" className="text-white">
                  Artist *
                </Label>
                <Input
                  id="artist"
                  value={formData.artist}
                  onChange={(e) => setFormData((prev) => ({ ...prev, artist: e.target.value }))}
                  className="bg-gray-800 border-gray-600 text-white"
                  placeholder="Enter artist name"
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="album" className="text-white">
                Album
              </Label>
              <Input
                id="album"
                value={formData.album}
                onChange={(e) => setFormData((prev) => ({ ...prev, album: e.target.value }))}
                className="bg-gray-800 border-gray-600 text-white"
                placeholder="Enter album name"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="genre" className="text-white">
                  Genre
                </Label>
                <Select
                  value={formData.genre}
                  onValueChange={(value) => setFormData((prev) => ({ ...prev, genre: value }))}
                >
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue placeholder="Select genre" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    {genres.map((genre) => (
                      <SelectItem key={genre} value={genre} className="text-white hover:bg-gray-700">
                        {genre}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="mood" className="text-white">
                  Mood
                </Label>
                <Select
                  value={formData.mood}
                  onValueChange={(value) => setFormData((prev) => ({ ...prev, mood: value }))}
                >
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue placeholder="Select mood" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-800 border-gray-600">
                    {moods.map((mood) => (
                      <SelectItem key={mood} value={mood} className="text-white hover:bg-gray-700">
                        {mood}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="tier" className="text-white">
                Access Tier
              </Label>
              <Select
                value={formData.tier.toString()}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, tier: Number.parseInt(value) as 0 | 1 | 2 }))
                }
              >
                <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                  <SelectValue placeholder="Select access tier" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  {tiers.map((tier) => (
                    <SelectItem key={tier.value} value={tier.value.toString()} className="text-white hover:bg-gray-700">
                      <div>
                        <div className="font-medium">{tier.label}</div>
                        <div className="text-sm text-gray-400">{tier.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="description" className="text-white">
                Description
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
                className="bg-gray-800 border-gray-600 text-white"
                placeholder="Enter track description"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={!audioFile || !formData.title || !formData.artist || isUploading}
          className="w-full bg-green-500 hover:bg-green-600 text-black font-medium"
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Upload Track
            </>
          )}
        </Button>
      </form>
    </div>
  )
}
