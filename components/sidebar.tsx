"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { motion } from "framer-motion"
import {
  Home,
  Search,
  Library,
  Heart,
  Plus,
  Upload,
  Users,
  Crown,
  Music,
  TrendingUp,
  PlayCircle,
  ListMusic,
  Radio,
  Album,
  ChevronDown,
  ChevronRight,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"

interface SidebarProps {
  userRole?: "user" | "admin"
  userTier?: 0 | 1 | 2 // free, pro, business
}

const userMenuItems = [
  {
    title: "Home",
    icon: Home,
    href: "/",
    badge: null,
  },
  {
    title: "Search",
    icon: Search,
    href: "/search",
    badge: null,
  },
  {
    title: "Your Library",
    icon: Library,
    href: "/library",
    badge: null,
  },
  {
    title: "Liked Songs",
    icon: Heart,
    href: "/liked",
    badge: null,
  },
]

const discoverItems = [
  {
    title: "Trending",
    icon: TrendingUp,
    href: "/trending",
    badge: "Hot",
  },
  {
    title: "New Releases",
    icon: PlayCircle,
    href: "/new-releases",
    badge: null,
  },
  {
    title: "Genres",
    icon: Album,
    href: "/genres",
    badge: null,
  },
  {
    title: "Radio",
    icon: Radio,
    href: "/radio",
    badge: null,
  },
]

const adminItems = [
  {
    title: "Dashboard",
    icon: TrendingUp,
    href: "/admin/dashboard",
    badge: null,
  },
  {
    title: "Users",
    icon: Users,
    href: "/admin/users",
    badge: null,
  },
  {
    title: "Music Library",
    icon: Music,
    href: "/admin/music",
    badge: null,
  },
  {
    title: "Upload Center",
    icon: Upload,
    href: "/admin/upload",
    badge: null,
  },
  {
    title: "Analytics",
    icon: TrendingUp,
    href: "/admin/analytics",
    badge: null,
  },
]

const playlists = [
  { name: "My Playlist #1", count: 23 },
  { name: "Chill Vibes", count: 45 },
  { name: "Workout Mix", count: 32 },
  { name: "Study Focus", count: 18 },
  { name: "Party Hits", count: 67 },
]

export function Sidebar({ userRole = "user", userTier = 0 }: SidebarProps) {
  const pathname = usePathname()
  const [isPlaylistsExpanded, setIsPlaylistsExpanded] = useState(true)

  const getTierBadge = () => {
    switch (userTier) {
      case 1:
        return <Badge className="bg-yellow-500 text-black">Pro</Badge>
      case 2:
        return <Badge className="bg-purple-500 text-white">Business</Badge>
      default:
        return <Badge variant="outline">Free</Badge>
    }
  }

  return (
    <div className="flex h-full w-64 flex-col bg-white text-black border-r border-gray-200">
      {/* Logo */}
      <div className="flex items-center gap-3 p-6">
        <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-orange-500">
          <Music className="h-5 w-5 text-white" />
        </div>
        <span className="text-xl font-bold text-orange-500">MusicPlatform</span>
      </div>

      <ScrollArea className="flex-1 px-3">
        {/* Main Navigation */}
        <div className="space-y-1">
          {userMenuItems.map((item) => (
            <Link key={item.href} href={item.href}>
              <Button
                variant="ghost"
                className={cn(
                  "w-full justify-start gap-3 text-gray-700 hover:text-black hover:bg-gray-100",
                  pathname === item.href && "bg-orange-100 text-orange-600",
                )}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.title}</span>
                {item.badge && (
                  <Badge variant="secondary" className="ml-auto">
                    {item.badge}
                  </Badge>
                )}
              </Button>
            </Link>
          ))}
        </div>

        <Separator className="my-4 bg-gray-200" />

        {/* Discover */}
        <div className="space-y-1">
          <h3 className="mb-2 px-3 text-sm font-semibold text-orange-500">Discover</h3>
          {discoverItems.map((item) => (
            <Link key={item.href} href={item.href}>
              <Button
                variant="ghost"
                className={cn(
                  "w-full justify-start gap-3 text-gray-700 hover:text-black hover:bg-gray-100",
                  pathname === item.href && "bg-orange-100 text-orange-600",
                )}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.title}</span>
                {item.badge && (
                  <Badge variant="secondary" className="ml-auto bg-red-500 text-white">
                    {item.badge}
                  </Badge>
                )}
              </Button>
            </Link>
          ))}
        </div>

        <Separator className="my-4 bg-gray-200" />

        {/* Upload */}
        <div className="space-y-1">
          <Link href="/upload">
            <Button
              variant="ghost"
              className={cn(
                "w-full justify-start gap-3 text-gray-700 hover:text-black hover:bg-gray-100",
                pathname === "/upload" && "bg-orange-100 text-orange-600",
              )}
            >
              <Upload className="h-5 w-5" />
              <span>Upload Music</span>
              {userTier === 0 && (
                <Badge variant="outline" className="ml-auto">
                  Limited
                </Badge>
              )}
            </Button>
          </Link>
        </div>

        <Separator className="my-4 bg-gray-200" />

        {/* Admin Section */}
        {userRole === "admin" && (
          <>
            <div className="space-y-1">
              <h3 className="mb-2 px-3 text-sm font-semibold text-orange-500">Admin</h3>
              {adminItems.map((item) => (
                <Link key={item.href} href={item.href}>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-start gap-3 text-gray-700 hover:text-black hover:bg-gray-100",
                      pathname === item.href && "bg-orange-100 text-orange-600",
                    )}
                  >
                    <item.icon className="h-5 w-5" />
                    <span>{item.title}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="ml-auto">
                        {item.badge}
                      </Badge>
                    )}
                  </Button>
                </Link>
              ))}
            </div>
            <Separator className="my-4 bg-gray-200" />
          </>
        )}

        {/* Playlists */}
        <div className="space-y-1">
          <Button
            variant="ghost"
            onClick={() => setIsPlaylistsExpanded(!isPlaylistsExpanded)}
            className="w-full justify-start gap-3 text-orange-500 hover:text-orange-600 hover:bg-gray-100"
          >
            {isPlaylistsExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
            <span className="text-sm font-semibold">Your Playlists</span>
          </Button>

          {isPlaylistsExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-1"
            >
              <Link href="/playlist/create">
                <Button
                  variant="ghost"
                  className="w-full justify-start gap-3 text-gray-700 hover:text-black hover:bg-gray-100"
                >
                  <Plus className="h-5 w-5" />
                  <span>Create Playlist</span>
                </Button>
              </Link>

              {playlists.map((playlist, index) => (
                <Link key={index} href={`/playlist/${index + 1}`}>
                  <Button
                    variant="ghost"
                    className="w-full justify-start gap-3 text-gray-700 hover:text-black hover:bg-gray-100"
                  >
                    <ListMusic className="h-5 w-5" />
                    <div className="flex flex-1 items-center justify-between">
                      <span className="truncate">{playlist.name}</span>
                      <span className="text-xs text-gray-600">{playlist.count}</span>
                    </div>
                  </Button>
                </Link>
              ))}
            </motion.div>
          )}
        </div>
      </ScrollArea>

      {/* Upgrade Section */}
      {userTier === 0 && (
        <div className="border-t border-gray-200 p-4">
          <div className="rounded-lg bg-gradient-to-r from-orange-500 to-red-500 p-4 text-center text-white">
            <Crown className="mx-auto mb-2 h-6 w-6" />
            <h4 className="font-semibold">Upgrade to Pro</h4>
            <p className="text-xs text-orange-100">Unlimited uploads & more</p>
            <Link href="/pricing">
              <Button size="sm" className="mt-2 w-full bg-white text-black hover:bg-gray-200">
                Upgrade Now
              </Button>
            </Link>
          </div>
        </div>
      )}

      <div className="border-t border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Current Plan</span>
          {getTierBadge()}
        </div>
      </div>
    </div>
  )
}
