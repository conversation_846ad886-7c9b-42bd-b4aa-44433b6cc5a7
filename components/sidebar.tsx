"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { motion } from "framer-motion"
import {
  Home,
  Upload,
  Users,
  Crown,
  Music,
  DollarSign,
  X,
  TrendingUp,
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"

interface SidebarProps {
  userRole?: "user" | "admin"
  userTier?: 0 | 1 | 2
  isMobile?: boolean
  onClose?: () => void
}

const userMenuItems = [
  {
    title: "Home",
    icon: Home,
    href: "/",
    badge: null,
  },
  // {
  //   title: "Search",
  //   icon: Search,
  //   href: "/search",
  //   badge: null,
  // },
  {
    title: "Bảng giá",
    icon: DollarSign,
    href: "/pricing",
    badge: null,
  },
]


const adminItems = [
  {
    title: "Dashboard",
    icon: TrendingUp,
    href: "/admin/dashboard",
    badge: null,
  },
  {
    title: "Users",
    icon: Users,
    href: "/admin/users",
    badge: null,
  },
  {
    title: "Music Library",
    icon: Music,
    href: "/admin/music",
    badge: null,
  },
  {
    title: "Upload Center",
    icon: Upload,
    href: "/admin/upload",
    badge: null,
  },
  {
    title: "Analytics",
    icon: TrendingUp,
    href: "/admin/analytics",
    badge: null,
  },
]

const playlists = [
  { name: "My Playlist #1", count: 23 },
  { name: "Chill Vibes", count: 45 },
  { name: "Workout Mix", count: 32 },
  { name: "Study Focus", count: 18 },
  { name: "Party Hits", count: 67 },
]

export function Sidebar({
  userRole = "user",
  userTier = 0,
  isMobile = false,
  onClose
}: SidebarProps) {
  const pathname = usePathname()

  const getTierBadge = () => {
    switch (userTier) {
      case 1:
        return <Badge className="bg-yellow-500 text-black">Pro</Badge>
      case 2:
        return <Badge className="bg-purple-500 text-white">Business</Badge>
      default:
        return <Badge variant="outline">Free</Badge>
    }
  }

  return (
    <div className={`flex h-full flex-col bg-white text-black border-r border-gray-200 ${
      isMobile ? 'w-80' : 'w-64'
    }`}>
      {/* Mobile Header with Close Button */}
      {isMobile && (
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-orange-500">
              <Music className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold text-orange-500">MusicPlatform</span>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
            className="text-gray-600 hover:text-black"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>
      )}

      {/* Desktop Logo */}
      {!isMobile && (
        <div className="flex items-center gap-3 p-6">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-orange-500">
            <Music className="h-5 w-5 text-white" />
          </div>
          <span className="text-xl font-bold text-orange-500">MusicPlatform</span>
        </div>
      )}

      <ScrollArea className="flex-1 px-3">
        <div className="space-y-1">
          {userMenuItems.map((item) => (
            <Link key={item.href} href={item.href}>
              <Button
                variant="ghost"
                className={cn(
                  "w-full justify-start gap-3 text-gray-700 hover:text-black hover:bg-gray-100",
                  pathname === item.href && "bg-orange-100 text-orange-600",
                )}
              >
                <item.icon className="h-5 w-5" />
                <span>{item.title}</span>
                {item.badge && (
                  <Badge variant="secondary" className="ml-auto">
                    {item.badge}
                  </Badge>
                )}
              </Button>
            </Link>
          ))}
        </div>

        {userRole === "admin" && (
          <>
            <div className="space-y-1">
              <h3 className="mb-2 px-3 text-sm font-semibold text-orange-500">Admin</h3>
              {adminItems.map((item) => (
                <Link key={item.href} href={item.href}>
                  <Button
                    variant="ghost"
                    className={cn(
                      "w-full justify-start gap-3 text-gray-700 hover:text-black hover:bg-gray-100",
                      pathname === item.href && "bg-orange-100 text-orange-600",
                    )}
                  >
                    <item.icon className="h-5 w-5" />
                    <span>{item.title}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="ml-auto">
                        {item.badge}
                      </Badge>
                    )}
                  </Button>
                </Link>
              ))}
            </div>
            <Separator className="my-4 bg-gray-200" />
          </>
        )}
      </ScrollArea>

      {userTier === 0 && (
        <div className="border-t border-gray-200 p-4">
          <div className="rounded-lg bg-gradient-to-r from-orange-500 to-red-500 p-4 text-center text-white">
            <Crown className="mx-auto mb-2 h-6 w-6" />
            <h4 className="font-semibold">Upgrade to Pro</h4>
            <p className="text-xs text-orange-100">Unlimited uploads & more</p>
            <Link href="/pricing">
              <Button size="sm" className="mt-2 w-full bg-white text-black hover:bg-gray-200">
                Upgrade Now
              </Button>
            </Link>
          </div>
        </div>
      )}

      <div className="border-t border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600">Current Plan</span>
          {getTierBadge()}
        </div>
      </div>
    </div>
  )
}
