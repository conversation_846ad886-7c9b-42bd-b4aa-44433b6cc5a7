"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { API_CONFIG } from "@/lib/api-config"

export function QuickApiTest() {
  const [testing, setTesting] = useState(false)
  const [result, setResult] = useState<string | null>(null)

  const testConnection = async () => {
    setTesting(true)
    setResult(null)

    try {
      console.log('Testing API connection to:', API_CONFIG.BASE_URL)
      
      // Test basic fetch to the API
      const response = await fetch(`${API_CONFIG.BASE_URL}/song?limit=1`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        credentials: 'include'
      })

      console.log('Response status:', response.status)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))

      if (response.ok) {
        const data = await response.json()
        console.log('Response data:', data)
        setResult(`✅ Success! Found ${data.total || data.items?.length || 0} songs`)
      } else {
        const errorText = await response.text()
        console.log('Error response:', errorText)
        setResult(`❌ Error ${response.status}: ${response.statusText}`)
      }
    } catch (error: any) {
      console.error('Connection test failed:', error)
      setResult(`❌ Connection failed: ${error.message}`)
    } finally {
      setTesting(false)
    }
  }

  return (
    <Card className="w-full max-w-md">
      <CardContent className="pt-6">
        <div className="text-center space-y-4">
          <div>
            <h3 className="font-semibold mb-2">Quick API Test</h3>
            <p className="text-sm text-gray-600 mb-4">
              Test connection to: <br />
              <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                {API_CONFIG.BASE_URL}
              </code>
            </p>
          </div>

          <Button 
            onClick={testConnection} 
            disabled={testing}
            className="w-full"
          >
            {testing ? 'Testing...' : 'Test Connection'}
          </Button>

          {result && (
            <div className={`p-3 rounded-md text-sm ${
              result.includes('✅') 
                ? 'bg-green-50 text-green-700 border border-green-200' 
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {result}
            </div>
          )}

          <div className="text-xs text-gray-500">
            Check browser console for detailed logs
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
