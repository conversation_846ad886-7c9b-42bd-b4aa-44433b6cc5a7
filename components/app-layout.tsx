"use client"

import { useState, useEffect } from "react"
import { Sidebar } from "@/components/sidebar"
import { Head<PERSON> } from "@/components/header"
import { LoginModal } from "@/components/login-modal"
import { useAuthStore } from "@/lib/auth-store"
import { Menu, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface AppLayoutProps {
  children: React.ReactNode
  showSidebar?: boolean
  showHeader?: boolean
}

export function AppLayout({
  children,
  showSidebar = true,
  showHeader = true
}: AppLayoutProps) {
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const { user } = useAuthStore()

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const getTierNumber = (tier: string): 0 | 1 | 2 => {
    switch (tier?.toUpperCase()) {
      case "PRO":
      case "1":
        return 1
      case "BUSINESS":
      case "2":
        return 2
      default:
        return 0
    }
  }

  const handleLoginSuccess = () => {
    // Refresh the page or update state as needed
    window.location.reload()
  }

  if (!showSidebar && !showHeader) {
    // For pages like login, register, pricing that don't need sidebar/header
    return <>{children}</>
  }

  return (
    <div className="flex h-screen bg-white text-black relative">
      {/* Mobile Sidebar Overlay */}
      {isMobile && showSidebar && isMobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsMobileSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      {showSidebar && (
        <div className={`${
          isMobile
            ? `fixed left-0 top-0 h-full z-50 transform transition-transform duration-300 ${
                isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full'
              }`
            : 'relative'
        }`}>
          <Sidebar
            userRole={user?.role as "user" | "admin" | undefined}
            userTier={getTierNumber(user?.tier?.toString() || "FREE")}
            isMobile={isMobile}
            onClose={() => setIsMobileSidebarOpen(false)}
          />
        </div>
      )}

      {/* Main Content */}
      <div className={`flex-1 flex flex-col ${showSidebar && !isMobile ? '' : 'w-full'}`}>
        {/* Mobile Header with Menu Button */}
        {isMobile && showSidebar && (
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsMobileSidebarOpen(true)}
              className="text-gray-600 hover:text-black"
            >
              <Menu className="h-6 w-6" />
            </Button>
            <div className="flex items-center gap-2">
              <div className="h-8 w-8 bg-orange-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">M</span>
              </div>
              <span className="font-bold text-orange-500">MusicPlatform</span>
            </div>
            <div className="w-10" /> {/* Spacer for centering */}
          </div>
        )}

        {/* Header */}
        {showHeader && !isMobile && (
          <Header
            user={user ? {
              name: user.name || user.email || 'User',
              email: user.email || '',
              avatar: user.avatar,
              role: (user.role as "user" | "admin") || "user",
              tier: getTierNumber(user.tier?.toString() || "FREE")
            } : undefined}
            onLoginClick={() => setShowLoginModal(true)}
          />
        )}

        {/* Content */}
        <main className={`flex-1 overflow-auto ${showSidebar && !isMobile ? 'pb-36' : isMobile ? 'pb-20' : ''}`}>
          {children}
        </main>
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
        onLoginSuccess={handleLoginSuccess}
      />
    </div>
  )
}
