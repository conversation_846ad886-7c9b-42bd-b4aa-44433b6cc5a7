"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Play, Pause, Download, Heart, MoreHorizontal, Plus, Clock, Music } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface Track {
  id: string
  title: string
  artist: string
  album?: string
  duration: number
  tempo: number
  tone: string
  genre: string
  mood: string
  coverUrl?: string
  audioUrl: string
  tier: 0 | 1 | 2 // Required tier to access
  isLiked?: boolean
}

interface TrackListProps {
  tracks: Track[]
  currentTrackId?: string
  isPlaying: boolean
  userTier: 0 | 1 | 2
  onPlay: (trackId: string) => void
  onPause: () => void
  onDownload: (trackId: string) => void
  onAddToPlaylist: (trackId: string) => void
  onToggleLike: (trackId: string) => void
}

export function TrackList({
  tracks,
  currentTrackId,
  isPlaying,
  userTier,
  onPlay,
  onPause,
  onDownload,
  onAddToPlaylist,
  onToggleLike,
}: TrackListProps) {
  const [hoveredTrack, setHoveredTrack] = useState<string | null>(null)

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }

  const canAccessTrack = (track: Track) => {
    return userTier >= track.tier
  }

  const getTierBadge = (tier: 0 | 1 | 2) => {
    switch (tier) {
      case 1:
        return <Badge className="bg-yellow-500 text-black text-xs">Pro</Badge>
      case 2:
        return <Badge className="bg-purple-500 text-white text-xs">Business</Badge>
      default:
        return (
          <Badge variant="outline" className="text-xs">
            Free
          </Badge>
        )
    }
  }

  const getMoodColor = (mood: string) => {
    const colors: Record<string, string> = {
      happy: "bg-yellow-500",
      sad: "bg-blue-500",
      energetic: "bg-red-500",
      calm: "bg-green-500",
      romantic: "bg-pink-500",
      dark: "bg-gray-700",
      uplifting: "bg-orange-500",
    }
    return colors[mood.toLowerCase()] || "bg-gray-500"
  }

  return (
    <div className="w-full">
      {/* Mobile View */}
      <div className="block md:hidden">
        <div className="space-y-4">
          {tracks.map((track) => {
            const isCurrentTrack = currentTrackId === track.id
            const isHovered = hoveredTrack === track.id
            const hasAccess = canAccessTrack(track)

            return (
              <div
                key={track.id}
                className={`p-4 rounded-lg border transition-all ${
                  isCurrentTrack ? "border-orange-500 bg-orange-50" : "border-gray-200 bg-white"
                } ${!hasAccess ? "opacity-50" : ""}`}
                onMouseEnter={() => setHoveredTrack(track.id)}
                onMouseLeave={() => setHoveredTrack(null)}
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className="h-12 w-12 rounded bg-gray-200 overflow-hidden relative">
                    {track.coverUrl ? (
                      <img
                        src={track.coverUrl || "/placeholder.svg"}
                        alt={track.title}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="flex h-full w-full items-center justify-center">
                        <Music className="h-4 w-4 text-gray-600" />
                      </div>
                    )}

                    {/* Play button overlay */}
                    {hasAccess && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-white hover:text-white"
                          onClick={() => {
                            if (isCurrentTrack && isPlaying) {
                              onPause()
                            } else {
                              onPlay(track.id)
                            }
                          }}
                        >
                          {isCurrentTrack && isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        </Button>
                      </div>
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <h3 className={`font-medium truncate ${isCurrentTrack ? "text-orange-500" : "text-black"}`}>
                      {track.title}
                    </h3>
                    <p className="text-sm text-gray-600 truncate">{track.artist}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs text-gray-500">{formatTime(track.duration)}</span>
                      <span className="text-xs text-gray-500">•</span>
                      <span className="text-xs text-gray-500">{track.genre}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`h-8 w-8 transition-colors ${
                        track.isLiked
                          ? "text-red-500 hover:text-red-600"
                          : "text-gray-400 hover:text-red-500 hover:bg-red-50"
                      }`}
                      onClick={() => onToggleLike(track.id)}
                    >
                      <Heart className={`h-4 w-4 ${track.isLiked ? "fill-current" : ""}`} />
                    </Button>

                    {hasAccess && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-400 hover:text-orange-500 hover:bg-orange-50 transition-colors"
                        onClick={() => onDownload(track.id)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Desktop View */}
      <div className="hidden md:block">
        <Table>
          <TableHeader>
            <TableRow className="border-gray-200 hover:bg-gray-50">
              <TableHead className="text-orange-500">Title</TableHead>
              {/* <TableHead>Artist</TableHead> */}
              <TableHead className=" text-orange-500">
                Thời gian
              </TableHead>
              <TableHead className=" text-orange-500">Tempo</TableHead>
              <TableHead className=" text-orange-500">Tone</TableHead>
              <TableHead className="text-orange-500">Genre</TableHead>
              <TableHead className="text-orange-500">Mood</TableHead>
              <TableHead className=" text-orange-500">Tier</TableHead>
              <TableHead className=" text-orange-500">Actions</TableHead>
            </TableRow>
          </TableHeader>
        <TableBody>
          {tracks.map((track) => {
            const isCurrentTrack = currentTrackId === track.id
            const isHovered = hoveredTrack === track.id
            const hasAccess = canAccessTrack(track)

            return (
              <motion.tr
                key={track.id}
                className={`border-gray-200 hover:bg-gray-50 ${
                  isCurrentTrack ? "bg-orange-50" : ""
                } ${!hasAccess ? "opacity-50" : ""}`}
                onMouseEnter={() => setHoveredTrack(track.id)}
                onMouseLeave={() => setHoveredTrack(null)}
                whileHover={{ backgroundColor: "rgba(249, 115, 22, 0.1)" }}
              >
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded bg-gray-200 overflow-hidden relative">
                      {track.coverUrl ? (
                        <img
                          src={track.coverUrl || "/placeholder.svg"}
                          alt={track.title}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center">
                          <Music className="h-4 w-4 text-gray-600" />
                        </div>
                      )}

                      {/* Play button overlay */}
                      {isHovered && hasAccess && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-white hover:text-white"
                            onClick={() => {
                              if (isCurrentTrack && isPlaying) {
                                onPause()
                              } else {
                                onPlay(track.id)
                              }
                            }}
                          >
                            {isCurrentTrack && isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                          </Button>
                        </div>
                      )}
                    </div>
                    <div>
                      <p className={`font-medium ${isCurrentTrack ? "text-orange-500" : "text-black"}`}>{track.title}</p>
                      {track.album && <p className="text-sm text-gray-600">{track.album}</p>}
                    </div>
                  </div>
                </TableCell>

                {/* <TableCell className="text-gray-700">{track.artist}</TableCell> */}

                <TableCell className="text-gray-600">{formatTime(track.duration)}</TableCell>

                <TableCell>
                  <Badge variant="outline" className="text-xs">
                    {track.tempo} BPM
                  </Badge>
                </TableCell>

                <TableCell>
                  <Badge variant="outline" className="text-xs">
                    {track.tone}
                  </Badge>
                </TableCell>

                <TableCell>
                  <Badge variant="secondary" className="text-xs">
                    {track.genre}
                  </Badge>
                </TableCell>

                <TableCell>
                  <Badge className={`${getMoodColor(track.mood)} text-white text-xs`}>{track.mood}</Badge>
                </TableCell>

                <TableCell>{getTierBadge(track.tier)}</TableCell>

                <TableCell>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`h-8 w-8 transition-colors ${
                        track.isLiked
                          ? "text-red-500 hover:text-red-600"
                          : "text-gray-400 hover:text-red-500 hover:bg-red-50"
                      }`}
                      onClick={() => onToggleLike(track.id)}
                    >
                      <Heart className={`h-4 w-4 ${track.isLiked ? "fill-current" : ""}`} />
                    </Button>

                    {hasAccess && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-400 hover:text-orange-500 hover:bg-orange-50 transition-colors"
                        onClick={() => onDownload(track.id)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </TableCell>
              </motion.tr>
            )
          })}
        </TableBody>
      </Table>

      {tracks.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12 text-gray-600">
          <Music className="h-12 w-12 mb-4" />
          <p className="text-lg font-medium">No tracks found</p>
          <p className="text-sm">Try adjusting your search or filters</p>
        </div>
      )}
      </div>

      {/* Empty State */}
      {tracks.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12 text-gray-600">
          <Music className="h-12 w-12 mb-4" />
          <p className="text-lg font-medium">No tracks found</p>
          <p className="text-sm">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  )
}
