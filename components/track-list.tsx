"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Play, Pause, Download, Heart, MoreHorizontal, Plus, Clock, Music } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface Track {
  id: string
  title: string
  artist: string
  album?: string
  duration: number
  tempo: number
  tone: string
  genre: string
  mood: string
  coverUrl?: string
  audioUrl: string
  tier: 0 | 1 | 2 // Required tier to access
  isLiked?: boolean
}

interface TrackListProps {
  tracks: Track[]
  currentTrackId?: string
  isPlaying: boolean
  userTier: 0 | 1 | 2
  onPlay: (trackId: string) => void
  onPause: () => void
  onDownload: (trackId: string) => void
  onAddToPlaylist: (trackId: string) => void
  onToggleLike: (trackId: string) => void
}

export function TrackList({
  tracks,
  currentTrackId,
  isPlaying,
  userTier,
  onPlay,
  onPause,
  onDownload,
  onAddToPlaylist,
  onToggleLike,
}: TrackListProps) {
  const [hoveredTrack, setHoveredTrack] = useState<string | null>(null)

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }

  const canAccessTrack = (track: Track) => {
    return userTier >= track.tier
  }

  const getTierBadge = (tier: 0 | 1 | 2) => {
    switch (tier) {
      case 1:
        return <Badge className="bg-yellow-500 text-black text-xs">Pro</Badge>
      case 2:
        return <Badge className="bg-purple-500 text-white text-xs">Business</Badge>
      default:
        return (
          <Badge variant="outline" className="text-xs">
            Free
          </Badge>
        )
    }
  }

  const getMoodColor = (mood: string) => {
    const colors: Record<string, string> = {
      happy: "bg-yellow-500",
      sad: "bg-blue-500",
      energetic: "bg-red-500",
      calm: "bg-green-500",
      romantic: "bg-pink-500",
      dark: "bg-gray-700",
      uplifting: "bg-orange-500",
    }
    return colors[mood.toLowerCase()] || "bg-gray-500"
  }

  return (
    <div className="w-full">
      <Table>
        <TableHeader>
          <TableRow className="border-gray-200 hover:bg-gray-50">
            <TableHead className="w-12 text-orange-500">#</TableHead>
            <TableHead className="text-orange-500">Title</TableHead>
            {/* <TableHead>Artist</TableHead> */}
            <TableHead className="w-20 text-orange-500">
              <Clock className="h-4 w-4" />
            </TableHead>
            <TableHead className="w-20 text-orange-500">Tempo</TableHead>
            <TableHead className="w-20 text-orange-500">Tone</TableHead>
            <TableHead className="text-orange-500">Genre</TableHead>
            <TableHead className="text-orange-500">Mood</TableHead>
            <TableHead className="w-20 text-orange-500">Tier</TableHead>
            <TableHead className="w-32 text-orange-500">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tracks.map((track, index) => {
            const isCurrentTrack = currentTrackId === track.id
            const isHovered = hoveredTrack === track.id
            const hasAccess = canAccessTrack(track)

            return (
              <motion.tr
                key={track.id}
                className={`border-gray-200 hover:bg-gray-50 ${
                  isCurrentTrack ? "bg-orange-50" : ""
                } ${!hasAccess ? "opacity-50" : ""}`}
                onMouseEnter={() => setHoveredTrack(track.id)}
                onMouseLeave={() => setHoveredTrack(null)}
                whileHover={{ backgroundColor: "rgba(249, 115, 22, 0.1)" }}
              >
                <TableCell>
                  <div className="flex items-center justify-center">
                    {isHovered && hasAccess ? (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => {
                          if (isCurrentTrack && isPlaying) {
                            onPause()
                          } else {
                            onPlay(track.id)
                          }
                        }}
                      >
                        {isCurrentTrack && isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </Button>
                    ) : (
                      <span className="text-gray-600">{index + 1}</span>
                    )}
                  </div>
                </TableCell>

                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded bg-gray-200 overflow-hidden">
                      {track.coverUrl ? (
                        <img
                          src={track.coverUrl || "/placeholder.svg"}
                          alt={track.title}
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="flex h-full w-full items-center justify-center">
                          <Music className="h-4 w-4 text-gray-600" />
                        </div>
                      )}
                    </div>
                    <div>
                      <p className={`font-medium ${isCurrentTrack ? "text-orange-500" : "text-black"}`}>{track.title}</p>
                      {track.album && <p className="text-sm text-gray-600">{track.album}</p>}
                    </div>
                  </div>
                </TableCell>

                {/* <TableCell className="text-gray-700">{track.artist}</TableCell> */}

                <TableCell className="text-gray-600">{formatTime(track.duration)}</TableCell>

                <TableCell>
                  <Badge variant="outline" className="text-xs">
                    {track.tempo} BPM
                  </Badge>
                </TableCell>

                <TableCell>
                  <Badge variant="outline" className="text-xs">
                    {track.tone}
                  </Badge>
                </TableCell>

                <TableCell>
                  <Badge variant="secondary" className="text-xs">
                    {track.genre}
                  </Badge>
                </TableCell>

                <TableCell>
                  <Badge className={`${getMoodColor(track.mood)} text-white text-xs`}>{track.mood}</Badge>
                </TableCell>

                <TableCell>{getTierBadge(track.tier)}</TableCell>

                <TableCell>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      className={`h-8 w-8 ${track.isLiked ? "text-red-500" : "text-gray-600"} hover:text-red-400`}
                      onClick={() => onToggleLike(track.id)}
                    >
                      <Heart className={`h-4 w-4 ${track.isLiked ? "fill-current" : ""}`} />
                    </Button>

                    {hasAccess && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-600 hover:text-black"
                        onClick={() => onDownload(track.id)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    )}

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-600 hover:text-black">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-white border-gray-200">
                        <DropdownMenuItem
                          onClick={() => onAddToPlaylist(track.id)}
                          className="text-gray-700 hover:bg-gray-100 hover:text-black"
                        >
                          <Plus className="mr-2 h-4 w-4" />
                          Add to Playlist
                        </DropdownMenuItem>
                        {hasAccess && (
                          <DropdownMenuItem
                            onClick={() => onDownload(track.id)}
                            className="text-gray-700 hover:bg-gray-100 hover:text-black"
                          >
                            <Download className="mr-2 h-4 w-4" />
                            Download
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </TableCell>
              </motion.tr>
            )
          })}
        </TableBody>
      </Table>

      {tracks.length === 0 && (
        <div className="flex flex-col items-center justify-center py-12 text-gray-600">
          <Music className="h-12 w-12 mb-4" />
          <p className="text-lg font-medium">No tracks found</p>
          <p className="text-sm">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  )
}
