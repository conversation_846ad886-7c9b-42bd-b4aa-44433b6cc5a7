"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Check, Crown, Zap, Upload, Download, Music, Users, Shield, Headphones, Sparkles } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"

const plans = [
  {
    id: "free",
    name: "Free",
    tier: 0,
    price: { monthly: 0, yearly: 0 },
    description: "Perfect for getting started",
    icon: Music,
    color: "from-gray-500 to-gray-600",
    features: [
      "5 uploads per month",
      "10MB max file size",
      "Basic audio analysis",
      "Standard quality streaming",
      "Community support",
      "Basic playlist creation",
    ],
    limitations: ["Limited downloads", "Ads supported", "No priority support"],
  },
  {
    id: "pro",
    name: "Pro",
    tier: 1,
    price: { monthly: 9.99, yearly: 99.99 },
    description: "For serious music creators",
    icon: Crown,
    color: "from-yellow-500 to-orange-500",
    popular: true,
    features: [
      "50 uploads per month",
      "100MB max file size",
      "Advanced audio analysis",
      "High quality streaming",
      "Priority support",
      "Unlimited playlists",
      "Download in multiple formats",
      "No ads",
      "Early access to new features",
    ],
    limitations: [],
  },
  {
    id: "business",
    name: "Business",
    tier: 2,
    price: { monthly: 29.99, yearly: 299.99 },
    description: "For teams and businesses",
    icon: Sparkles,
    color: "from-purple-500 to-indigo-600",
    features: [
      "200 uploads per month",
      "500MB max file size",
      "Premium audio analysis",
      "Lossless quality streaming",
      "24/7 dedicated support",
      "Team collaboration tools",
      "Advanced analytics",
      "White-label options",
      "API access",
      "Custom integrations",
      "Bulk operations",
      "No ads",
    ],
    limitations: [],
  },
]

const features = [
  {
    icon: Upload,
    title: "Easy Upload",
    description: "Drag and drop your music files with automatic metadata extraction",
  },
  {
    icon: Zap,
    title: "AI Analysis",
    description: "Automatic tempo, tone, and mood detection using advanced algorithms",
  },
  {
    icon: Download,
    title: "Multiple Formats",
    description: "Download in various formats and quality levels",
  },
  {
    icon: Users,
    title: "Collaboration",
    description: "Share and collaborate with team members and artists",
  },
  {
    icon: Shield,
    title: "Secure Storage",
    description: "Your music is safely stored with enterprise-grade security",
  },
  {
    icon: Headphones,
    title: "High Quality",
    description: "Stream and download in up to lossless quality",
  },
]

export function PricingPage() {
  const [isYearly, setIsYearly] = useState(false)

  const handleSubscribe = (planId: string) => {
    // Handle subscription logic
    console.log(`Subscribing to ${planId} plan`)
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-20">
        <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 via-blue-500/10 to-purple-500/10" />
        <div className="relative mx-auto max-w-7xl px-6">
          <div className="text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-5xl font-bold mb-6"
            >
              Choose Your Music Journey
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-xl text-gray-400 mb-8 max-w-2xl mx-auto"
            >
              Unlock the full potential of your music with our comprehensive platform. From free exploration to
              professional production.
            </motion.p>

            {/* Billing Toggle */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="flex items-center justify-center gap-4 mb-12"
            >
              <span className={`text-sm ${!isYearly ? "text-white" : "text-gray-400"}`}>Monthly</span>
              <Switch checked={isYearly} onCheckedChange={setIsYearly} className="data-[state=checked]:bg-green-500" />
              <span className={`text-sm ${isYearly ? "text-white" : "text-gray-400"}`}>Yearly</span>
              <Badge className="bg-green-500 text-black">Save 20%</Badge>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-20">
        <div className="mx-auto max-w-7xl px-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {plans.map((plan, index) => (
              <motion.div
                key={plan.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="relative"
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-green-500 text-black px-4 py-1">Most Popular</Badge>
                  </div>
                )}

                <Card
                  className={`h-full bg-gray-900 border-gray-700 ${plan.popular ? "border-green-500 border-2" : ""}`}
                >
                  <CardHeader className="text-center pb-8">
                    <div
                      className={`w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r ${plan.color} flex items-center justify-center`}
                    >
                      <plan.icon className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-2xl font-bold text-white">{plan.name}</CardTitle>
                    <p className="text-gray-400">{plan.description}</p>
                    <div className="mt-4">
                      <div className="text-4xl font-bold text-white">
                        ${isYearly ? plan.price.yearly : plan.price.monthly}
                        {plan.price.monthly > 0 && (
                          <span className="text-lg text-gray-400">/{isYearly ? "year" : "month"}</span>
                        )}
                      </div>
                      {isYearly && plan.price.monthly > 0 && (
                        <p className="text-sm text-gray-400 mt-1">
                          ${(plan.price.monthly * 12).toFixed(2)} billed annually
                        </p>
                      )}
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-6">
                    <div className="space-y-3">
                      {plan.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center gap-3">
                          <Check className="h-5 w-5 text-green-500 flex-shrink-0" />
                          <span className="text-gray-300">{feature}</span>
                        </div>
                      ))}
                    </div>

                    <Button
                      onClick={() => handleSubscribe(plan.id)}
                      className={`w-full ${
                        plan.popular
                          ? "bg-green-500 hover:bg-green-600 text-black"
                          : "bg-gray-700 hover:bg-gray-600 text-white"
                      }`}
                    >
                      {plan.price.monthly === 0 ? "Get Started" : "Subscribe Now"}
                    </Button>

                    {plan.limitations.length > 0 && (
                      <div className="pt-4 border-t border-gray-700">
                        <p className="text-sm text-gray-400 mb-2">Limitations:</p>
                        <div className="space-y-1">
                          {plan.limitations.map((limitation, limitIndex) => (
                            <p key={limitIndex} className="text-sm text-gray-500">
                              • {limitation}
                            </p>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-900">
        <div className="mx-auto max-w-7xl px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-white mb-4">Everything You Need</h2>
            <p className="text-gray-400 max-w-2xl mx-auto">
              Our platform provides all the tools and features you need to manage, analyze, and distribute your music
              effectively.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">{feature.title}</h3>
                <p className="text-gray-400">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20">
        <div className="mx-auto max-w-4xl px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-white mb-4">Frequently Asked Questions</h2>
          </div>

          <div className="space-y-6">
            {[
              {
                question: "Can I upgrade or downgrade my plan anytime?",
                answer:
                  "Yes, you can change your subscription plan at any time. Changes will be reflected in your next billing cycle.",
              },
              {
                question: "What audio formats are supported?",
                answer:
                  "We support MP3, WAV, FLAC, AAC, and OGG formats for upload. Downloads are available in multiple formats based on your plan.",
              },
              {
                question: "How does the AI audio analysis work?",
                answer:
                  "Our system uses advanced algorithms including aubio library to automatically detect tempo, key, mood, and other musical characteristics of your tracks.",
              },
              {
                question: "Is there a free trial for paid plans?",
                answer:
                  "Yes, we offer a 14-day free trial for both Pro and Business plans. No credit card required to start.",
              },
              {
                question: "What happens to my music if I cancel?",
                answer:
                  "Your music remains accessible for 30 days after cancellation. You can download your files during this period.",
              },
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-900 rounded-lg p-6"
              >
                <h3 className="text-lg font-semibold text-white mb-2">{faq.question}</h3>
                <p className="text-gray-400">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-green-500 to-blue-500">
        <div className="mx-auto max-w-4xl px-6 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Ready to Start Your Music Journey?</h2>
          <p className="text-xl text-white/90 mb-8">Join thousands of artists and creators who trust our platform</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-white text-black hover:bg-gray-200">
              Start Free Trial
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-black bg-transparent"
            >
              Contact Sales
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
