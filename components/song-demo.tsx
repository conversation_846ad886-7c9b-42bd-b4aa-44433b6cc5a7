"use client"

import { useState, useEffect } from "react"
import { songApi, type Song } from "@/lib/api"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Play, Pause, Music, Clock, User, Zap } from "lucide-react"

export function SongDemo() {
  const [songs, setSongs] = useState<Song[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentlyPlaying, setCurrentlyPlaying] = useState<number | null>(null)

  // Load songs on component mount
  useEffect(() => {
    loadAllSongs()
  }, [])

  const loadAllSongs = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await songApi.searchSongs({ limit: 20 })
      setSongs(response.items)
      console.log('Loaded songs:', response)
    } catch (err: any) {
      setError(err.message || "Failed to load songs")
      console.error('Error loading songs:', err)
    } finally {
      setLoading(false)
    }
  }

  const searchByName = async (songName: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await songApi.getSongByName(songName)
      setSongs(response.items)
      console.log(`Search results for "${songName}":`, response)
    } catch (err: any) {
      setError(err.message || "Failed to search songs")
      console.error('Error searching songs:', err)
    } finally {
      setLoading(false)
    }
  }

  const searchByGenre = async (genre: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await songApi.getSongsByGenre(genre)
      setSongs(response.data)
      console.log(`Songs in ${genre} genre:`, response)
    } catch (err: any) {
      setError(err.message || "Failed to search by genre")
      console.error('Error searching by genre:', err)
    } finally {
      setLoading(false)
    }
  }

  const searchByMood = async (mood: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await songApi.getSongsByMood(mood)
      setSongs(response.data)
      console.log(`Songs with ${mood} mood:`, response)
    } catch (err: any) {
      setError(err.message || "Failed to search by mood")
      console.error('Error searching by mood:', err)
    } finally {
      setLoading(false)
    }
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'PRO':
        return 'bg-blue-100 text-blue-800'
      case 'BUSINESS':
        return 'bg-purple-100 text-purple-800'
      case 'FREE':
      default:
        return 'bg-green-100 text-green-800'
    }
  }

  const togglePlay = (songId: number) => {
    if (currentlyPlaying === songId) {
      setCurrentlyPlaying(null)
    } else {
      setCurrentlyPlaying(songId)
    }
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col gap-4">
        <h1 className="text-2xl font-bold">Song API Demo</h1>
        
        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button onClick={loadAllSongs} disabled={loading}>
            Load All Songs
          </Button>
          <Button onClick={() => searchByName('Havana')} disabled={loading} variant="outline">
            Search "Havana"
          </Button>
          <Button onClick={() => searchByName('Shape of You')} disabled={loading} variant="outline">
            Search "Shape of You"
          </Button>
          <Button onClick={() => searchByGenre('Pop')} disabled={loading} variant="outline">
            Pop Genre
          </Button>
          <Button onClick={() => searchByMood('Romantic')} disabled={loading} variant="outline">
            Romantic Mood
          </Button>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2">Loading songs...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <p className="text-red-600">{error}</p>
            </CardContent>
          </Card>
        )}

        {/* Results */}
        {!loading && songs.length > 0 && (
          <div className="space-y-4">
            <h2 className="text-lg font-semibold">
              Found {songs.length} songs
            </h2>
            
            <div className="grid gap-4">
              {songs.map((song) => (
                <Card key={song.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 space-y-3">
                        {/* Song Title and Artist */}
                        <div className="flex items-center gap-3">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => togglePlay(song.id)}
                            className="h-8 w-8 p-0"
                          >
                            {currentlyPlaying === song.id ? (
                              <Pause className="h-4 w-4" />
                            ) : (
                              <Play className="h-4 w-4" />
                            )}
                          </Button>
                          <div>
                            <h3 className="font-semibold text-lg">{song.songName}</h3>
                            <p className="text-gray-600 flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {song.artistName}
                            </p>
                          </div>
                          <Badge className={getTierColor(song.tier)}>
                            {song.tier}
                          </Badge>
                        </div>

                        {/* Song Details */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3 text-gray-500" />
                            <span>{formatDuration(song.duration)}</span>
                          </div>
                          {song.tempo && (
                            <div className="flex items-center gap-1">
                              <Zap className="h-3 w-3 text-gray-500" />
                              <span>{song.tempo} BPM</span>
                            </div>
                          )}
                          {song.pitch && (
                            <div className="text-gray-600">
                              Pitch: {song.pitch}
                            </div>
                          )}
                          <div className="text-gray-500 text-xs">
                            ID: {song.id}
                          </div>
                        </div>

                        {/* Tags */}
                        <div className="flex gap-2 flex-wrap">
                          {song.genre && (
                            <Badge variant="secondary">{song.genre}</Badge>
                          )}
                          {song.mood && (
                            <Badge variant="outline">{song.mood}</Badge>
                          )}
                        </div>

                        {/* Audio URL */}
                        <div className="text-xs text-gray-500 break-all">
                          <strong>Audio:</strong> {song.fileUrl}
                        </div>

                        {/* Timestamps */}
                        <div className="text-xs text-gray-400">
                          Created: {new Date(song.createdAt).toLocaleString()}
                        </div>
                      </div>

                      {/* Cover Image */}
                      {song.imageUrl && (
                        <div className="ml-4">
                          <img
                            src={song.imageUrl}
                            alt={`${song.songName} cover`}
                            className="w-20 h-20 rounded-lg object-cover"
                            onError={(e) => {
                              e.currentTarget.style.display = 'none'
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* No Results */}
        {!loading && songs.length === 0 && !error && (
          <Card>
            <CardContent className="pt-6 text-center">
              <Music className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No songs found. Try loading all songs or searching for something specific.</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
