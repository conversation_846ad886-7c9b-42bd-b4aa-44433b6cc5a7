"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { API_CONFIG, healthCheck } from "@/lib/api-config"
import { songApi } from "@/lib/api"
import { CheckCircle, XCircle, AlertCircle, RefreshCw } from "lucide-react"

export function ApiDebug() {
  const [testing, setTesting] = useState(false)
  const [results, setResults] = useState<{
    baseUrl: string
    healthCheck: { status: 'success' | 'error' | 'pending', message: string }
    songApi: { status: 'success' | 'error' | 'pending', message: string, data?: any }
    cors: { status: 'success' | 'error' | 'pending', message: string }
  }>({
    baseUrl: API_CONFIG.BASE_URL,
    healthCheck: { status: 'pending', message: 'Not tested' },
    songApi: { status: 'pending', message: 'Not tested' },
    cors: { status: 'pending', message: 'Not tested' }
  })

  const runTests = async () => {
    setTesting(true)
    
    // Test 1: Health Check
    setResults(prev => ({
      ...prev,
      healthCheck: { status: 'pending', message: 'Testing...' }
    }))
    
    try {
      const isHealthy = await healthCheck()
      setResults(prev => ({
        ...prev,
        healthCheck: { 
          status: isHealthy ? 'success' : 'error', 
          message: isHealthy ? 'Health check passed' : 'Health check failed' 
        }
      }))
    } catch (error: any) {
      setResults(prev => ({
        ...prev,
        healthCheck: { 
          status: 'error', 
          message: `Health check error: ${error.message}` 
        }
      }))
    }

    // Test 2: CORS Check
    setResults(prev => ({
      ...prev,
      cors: { status: 'pending', message: 'Testing CORS...' }
    }))
    
    try {
      const response = await fetch(API_CONFIG.BASE_URL, {
        method: 'OPTIONS',
        headers: {
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type'
        }
      })
      
      setResults(prev => ({
        ...prev,
        cors: { 
          status: response.ok ? 'success' : 'error', 
          message: response.ok ? 'CORS configured correctly' : `CORS error: ${response.status}` 
        }
      }))
    } catch (error: any) {
      setResults(prev => ({
        ...prev,
        cors: { 
          status: 'error', 
          message: `CORS error: ${error.message}` 
        }
      }))
    }

    // Test 3: Song API
    setResults(prev => ({
      ...prev,
      songApi: { status: 'pending', message: 'Testing song API...' }
    }))
    
    try {
      const response = await songApi.searchSongs({ limit: 1 })
      setResults(prev => ({
        ...prev,
        songApi: { 
          status: 'success', 
          message: `Song API working. Found ${response.total} songs`,
          data: response
        }
      }))
    } catch (error: any) {
      setResults(prev => ({
        ...prev,
        songApi: { 
          status: 'error', 
          message: `Song API error: ${error.message}`,
          data: error
        }
      }))
    }

    setTesting(false)
  }

  const getStatusIcon = (status: 'success' | 'error' | 'pending') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'pending':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: 'success' | 'error' | 'pending') => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800">Success</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800">Error</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-6 w-6" />
            API Debug Tool
          </CardTitle>
          <Button 
            onClick={runTests} 
            disabled={testing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${testing ? 'animate-spin' : ''}`} />
            {testing ? 'Testing...' : 'Run Tests'}
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Configuration */}
        <div>
          <h3 className="font-semibold mb-2">Configuration</h3>
          <div className="bg-gray-100 p-3 rounded-md">
            <p><strong>Base URL:</strong> {results.baseUrl}</p>
            <p><strong>Environment:</strong> {process.env.NODE_ENV}</p>
            <p><strong>Timeout:</strong> {API_CONFIG.TIMEOUT}ms</p>
            <p><strong>Retry Attempts:</strong> {API_CONFIG.RETRY_ATTEMPTS}</p>
          </div>
        </div>

        {/* Test Results */}
        <div className="space-y-4">
          <h3 className="font-semibold">Test Results</h3>
          
          {/* Health Check */}
          <div className="flex items-center justify-between p-3 border rounded-md">
            <div className="flex items-center gap-3">
              {getStatusIcon(results.healthCheck.status)}
              <div>
                <p className="font-medium">Health Check</p>
                <p className="text-sm text-gray-600">{results.healthCheck.message}</p>
              </div>
            </div>
            {getStatusBadge(results.healthCheck.status)}
          </div>

          {/* CORS Check */}
          <div className="flex items-center justify-between p-3 border rounded-md">
            <div className="flex items-center gap-3">
              {getStatusIcon(results.cors.status)}
              <div>
                <p className="font-medium">CORS Configuration</p>
                <p className="text-sm text-gray-600">{results.cors.message}</p>
              </div>
            </div>
            {getStatusBadge(results.cors.status)}
          </div>

          {/* Song API */}
          <div className="flex items-center justify-between p-3 border rounded-md">
            <div className="flex items-center gap-3">
              {getStatusIcon(results.songApi.status)}
              <div>
                <p className="font-medium">Song API</p>
                <p className="text-sm text-gray-600">{results.songApi.message}</p>
                {results.songApi.data && (
                  <details className="mt-2">
                    <summary className="text-xs text-blue-600 cursor-pointer">View Response</summary>
                    <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                      {JSON.stringify(results.songApi.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            </div>
            {getStatusBadge(results.songApi.status)}
          </div>
        </div>

        {/* Troubleshooting */}
        <div>
          <h3 className="font-semibold mb-2">Troubleshooting</h3>
          <div className="bg-blue-50 p-3 rounded-md text-sm">
            <p className="font-medium mb-2">Common Issues:</p>
            <ul className="list-disc list-inside space-y-1">
              <li><strong>CORS Error:</strong> Backend needs to allow your domain in CORS settings</li>
              <li><strong>Network Error:</strong> Check if backend server is running on {results.baseUrl}</li>
              <li><strong>Timeout:</strong> Backend might be slow or unreachable</li>
              <li><strong>404 Error:</strong> API endpoint might not exist or URL is incorrect</li>
            </ul>
          </div>
        </div>

        {/* Manual Test */}
        <div>
          <h3 className="font-semibold mb-2">Manual Test</h3>
          <div className="bg-gray-100 p-3 rounded-md">
            <p className="text-sm mb-2">Test the API directly in your browser:</p>
            <a 
              href={`${results.baseUrl}/song`} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline text-sm"
            >
              {results.baseUrl}/song
            </a>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
