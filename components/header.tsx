"use client"

import type React from "react"

import { useState } from "react"
import { Search, Bell, Settings, LogOut, User, Crown } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"

interface HeaderProps {
  user?: {
    name: string
    email: string
    avatar?: string
    role: "user" | "admin"
    tier: 0 | 1 | 2
  }
  onLoginClick?: () => void
}

export function Header({ user, onLoginClick }: HeaderProps) {
  const [searchQuery, setSearchQuery] = useState("")

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // Navigate to search results
      window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`
    }
  }

  const handleLogout = async () => {
    try {
      const { authApi } = await import("@/lib/auth-api")
      await authApi.logout()
      window.location.href = "/login"
    } catch (error) {
      console.error("Logout failed:", error)
    }
  }

  const getTierLabel = () => {
    switch (user?.tier) {
      case 1:
        return "Pro"
      case 2:
        return "Business"
      default:
        return "Free"
    }
  }

  const getTierColor = () => {
    switch (user?.tier) {
      case 1:
        return "bg-yellow-500 text-black"
      case 2:
        return "bg-purple-500 text-white"
      default:
        return "bg-gray-500 text-white"
    }
  }

  return (
    <header className="flex h-16 items-center justify-between border-b border-gray-200 bg-white px-6 text-black">
      {/* Search Bar */}
      <div className="flex flex-1 items-center gap-4">
        <form onSubmit={handleSearch} className="relative max-w-md flex-1">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-600" />
          <Input
            type="search"
            placeholder="Search songs, artists, albums..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full rounded-full border-gray-300 bg-gray-50 pl-10 pr-4 text-black placeholder-gray-600 focus:border-orange-500 focus:ring-orange-500"
          />
        </form>
      </div>

      <div className="flex items-center gap-4">
        {user ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center gap-3 hover:bg-gray-100">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
                  <AvatarFallback className="bg-orange-500 text-white">
                    {user.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="hidden text-left md:block">
                  <p className="text-sm font-medium">{user.name}</p>
                  <div className="flex items-center gap-2">
                    <Badge className={`text-xs ${getTierColor()}`}>{getTierLabel()}</Badge>
                    {user.role === "admin" && (
                      <Badge variant="outline" className="text-xs">
                        Admin
                      </Badge>
                    )}
                  </div>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 bg-white border-gray-200">
              <DropdownMenuLabel className="text-black">
                <div>
                  <p className="font-medium">{user.name}</p>
                  <p className="text-sm text-gray-600">{user.email}</p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-gray-200" />

              <DropdownMenuItem className="text-gray-700 hover:bg-gray-100 hover:text-black">
                <User className="mr-2 h-4 w-4" />
                Profile
              </DropdownMenuItem>

              <DropdownMenuItem className="text-gray-700 hover:bg-gray-100 hover:text-black">
                <Settings className="mr-2 h-4 w-4" />
                Settings
              </DropdownMenuItem>

              {user.tier === 0 && (
                <DropdownMenuItem className="text-gray-700 hover:bg-gray-100 hover:text-black">
                  <Crown className="mr-2 h-4 w-4" />
                  Upgrade to Pro
                </DropdownMenuItem>
              )}

              <DropdownMenuSeparator className="bg-gray-200" />

              <DropdownMenuItem onClick={handleLogout} className="text-red-600 hover:bg-gray-100 hover:text-red-700">
                <LogOut className="mr-2 h-4 w-4" />
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="ghost"
              className="text-gray-700 hover:text-black"
              onClick={onLoginClick}
            >
              Log in
            </Button>
            <Button
              className="bg-orange-500 text-white hover:bg-orange-600"
              onClick={() => window.location.href = '/register'}
            >
              Sign up
            </Button>
          </div>
        )}
      </div>
    </header>
  )
}
