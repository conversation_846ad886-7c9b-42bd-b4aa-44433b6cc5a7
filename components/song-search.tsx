"use client"

import { useState, useEffect } from "react"
import { song<PERSON>pi, type Song, type SongSearchParams } from "@/lib/api"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Search, Music, Clock, User, Album } from "lucide-react"

interface SongSearchProps {
  className?: string
}

export function SongSearch({ className }: SongSearchProps) {
  const [songs, setSongs] = useState<Song[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [searchParams, setSearchParams] = useState<SongSearchParams>({
    search: "",
    page: 1,
    limit: 20,
  })
  const [totalPages, setTotalPages] = useState(0)

  // Search songs function
  const searchSongs = async (params: SongSearchParams) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await songApi.searchSongs(params)
      setSongs(response.data)
      setTotalPages(response.totalPages)
    } catch (err: any) {
      setError(err.message || "Failed to search songs")
      setSongs([])
    } finally {
      setLoading(false)
    }
  }

  // Search by specific song name (as shown in your example)
  const searchBySongName = async (songName: string) => {
    setLoading(true)
    setError(null)

    try {
      const response = await songApi.getSongByName(songName)
      setSongs(response.items)
      setTotalPages(Math.ceil(response.total / (searchParams.limit || 20)))
    } catch (err: any) {
      setError(err.message || "Failed to search songs")
      setSongs([])
    } finally {
      setLoading(false)
    }
  }

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchParams.search?.trim()) {
      // Use specific song name search if it's a simple search
      if (searchParams.search && !searchParams.genre && !searchParams.mood) {
        searchBySongName(searchParams.search)
      } else {
        searchSongs({ ...searchParams, page: 1 })
      }
    }
  }

  // Handle input changes
  const handleInputChange = (field: keyof SongSearchParams, value: string) => {
    setSearchParams(prev => ({
      ...prev,
      [field]: value,
    }))
  }

  // Load initial songs on component mount
  useEffect(() => {
    searchSongs({ page: 1, limit: 20 })
  }, [])

  // Format duration from seconds to mm:ss
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Search Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Songs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Input
                placeholder="Search songs, artists..."
                value={searchParams.search || ""}
                onChange={(e) => handleInputChange("search", e.target.value)}
              />
              <Input
                placeholder="Genre"
                value={searchParams.genre || ""}
                onChange={(e) => handleInputChange("genre", e.target.value)}
              />
              <Input
                placeholder="Mood"
                value={searchParams.mood || ""}
                onChange={(e) => handleInputChange("mood", e.target.value)}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder="Artist name"
                value={searchParams.artistName || ""}
                onChange={(e) => handleInputChange("artistName", e.target.value)}
              />
              <Input
                placeholder="Song name"
                value={searchParams.songName || ""}
                onChange={(e) => handleInputChange("songName", e.target.value)}
              />
            </div>
            <Button type="submit" disabled={loading} className="w-full">
              {loading ? "Searching..." : "Search"}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <p className="text-red-600">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">
          {songs.length > 0 ? `Found ${songs.length} songs` : "No songs found"}
        </h3>
        
        {songs.map((song) => (
          <Card key={song.id} className="hover:shadow-md transition-shadow">
            <CardContent className="pt-6">
              <div className="flex items-start justify-between">
                <div className="flex-1 space-y-2">
                  <div className="flex items-center gap-2">
                    <Music className="h-4 w-4 text-green-500" />
                    <h4 className="font-semibold">{song.songName}</h4>
                    <Badge variant="outline">{song.tier}</Badge>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      {song.artistName}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatDuration(song.duration)}
                    </div>
                    {song.tempo && (
                      <div>
                        {song.tempo} BPM
                      </div>
                    )}
                    {song.pitch && (
                      <div>
                        Pitch: {song.pitch}
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2">
                    {song.genre && <Badge variant="secondary">{song.genre}</Badge>}
                    {song.mood && <Badge variant="outline">{song.mood}</Badge>}
                  </div>
                </div>

                {song.imageUrl && (
                  <img
                    src={song.imageUrl}
                    alt={`${song.songName} cover`}
                    className="w-16 h-16 rounded-md object-cover"
                  />
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            disabled={searchParams.page === 1}
            onClick={() => {
              const newParams = { ...searchParams, page: (searchParams.page || 1) - 1 }
              setSearchParams(newParams)
              searchSongs(newParams)
            }}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {searchParams.page} of {totalPages}
          </span>
          <Button
            variant="outline"
            disabled={searchParams.page === totalPages}
            onClick={() => {
              const newParams = { ...searchParams, page: (searchParams.page || 1) + 1 }
              setSearchParams(newParams)
              searchSongs(newParams)
            }}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}
