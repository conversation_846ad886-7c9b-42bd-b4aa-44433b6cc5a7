"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { authApi } from "@/lib/auth-api"
import { Loader2, Music } from "lucide-react"

interface LoginModalProps {
  isOpen: boolean
  onClose: () => void
  onLoginSuccess: () => void
  title?: string
  description?: string
}

export function LoginModal({ 
  isOpen, 
  onClose, 
  onLoginSuccess,
  title = "Login Required",
  description = "Please login to download songs"
}: LoginModalProps) {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      const response = await authApi.login({ email, password })

      // Check if response has access_token (successful login)
      if (response && response.access_token && response.user) {
        // Store tokens
        localStorage.setItem('access_token', response.access_token)
        localStorage.setItem('refresh_token', response.refresh_token)

        // Store user info
        localStorage.setItem('user', JSON.stringify(response.user))

        onLoginSuccess()
        onClose()
        // Reset form
        setEmail("")
        setPassword("")
      } else {
        setError("Login failed - Invalid response from server")
      }
    } catch (err: any) {
      console.error('Login error:', err)
      setError(err.message || "Login failed")
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setError(null)
    setEmail("")
    setPassword("")
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md bg-white text-black">
        <DialogHeader>
          <div className="flex items-center gap-2 mb-2">
            <Music className="h-6 w-6 text-orange-500" />
            <DialogTitle className="text-orange-500">{title}</DialogTitle>
          </div>
          <DialogDescription className="text-gray-600">
            {description}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              {error}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="email" className="text-black">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="Enter your email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={loading}
              className="bg-white border-gray-300 text-black placeholder-gray-500 focus:border-orange-500 focus:ring-orange-500"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-black">Password</Label>
            <Input
              id="password"
              type="password"
              placeholder="Enter your password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              disabled={loading}
              className="bg-white border-gray-300 text-black placeholder-gray-500 focus:border-orange-500 focus:ring-orange-500"
            />
          </div>

          <div className="flex gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
              className="flex-1 border-gray-300 text-black hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading || !email || !password}
              className="flex-1 bg-orange-500 text-white hover:bg-orange-600"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Logging in...
                </>
              ) : (
                "Login"
              )}
            </Button>
          </div>
        </form>

        <div className="text-center text-sm text-gray-600 mt-4">
          Don't have an account?{" "}
          <button
            type="button"
            className="text-orange-500 hover:underline"
            onClick={() => {
              // Navigate to register page or open register modal
              window.location.href = '/register'
            }}
          >
            Sign up
          </button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
