# API Structure Documentation

## Overview
The API has been organized into separate modules with centralized configuration for better maintainability, error handling, and scalability.

## File Structure

```
lib/
├── api-config.ts   # Centralized API configuration and HTTP client
├── api.ts          # Song API and Upload API
├── auth-api.ts     # Authentication API (separate file)
└── auth-guard.tsx  # Authentication guard component

examples/
├── api-usage.ts    # Song and Upload API examples
└── auth-usage.ts   # Authentication API examples

components/
└── song-search.tsx # Song search component example

.env.example        # Environment configuration template
```

## API Modules

### 0. API Configuration (`lib/api-config.ts`)
Centralized HTTP client with advanced features:

```typescript
import { api, apiClient, API_CONFIG } from '@/lib/api-config'

// Use convenience methods
const songs = await api.get('/song')
const result = await api.post('/auth/login', { email, password })

// Or use the main client directly
const response = await apiClient('/song', {
  method: 'GET',
  timeout: 10000,
  retries: 2
})
```

**Features:**
- Environment-based URL configuration
- Automatic retry with exponential backoff
- Request/response logging
- Timeout handling
- Comprehensive error handling
- Support for different content types (JSON, FormData)

### 1. Song API (`lib/api.ts`)
Handles all song-related operations:

```typescript
import { songApi } from '@/lib/api'

// Search by song name (your example)
const songs = await songApi.getSongByName('phat')

// Advanced search
const results = await songApi.advancedSearch({
  search: 'love',
  genre: 'pop',
  mood: 'happy'
})
```

**Available Methods:**
- `searchSongs(params)` - General search with filters
- `getSongByName(songName)` - Search by specific song name
- `getSongById(id)` - Get song by ID
- `advancedSearch(params)` - Advanced search with multiple filters
- `getSongsByGenre(genre)` - Filter by genre
- `getSongsByMood(mood)` - Filter by mood
- `getSongsByArtist(artistName)` - Filter by artist

### 2. Authentication API (`lib/auth-api.ts`)
Handles all authentication operations:

```typescript
import { authApi, authHelpers } from '@/lib/auth-api'

// Login
const response = await authApi.login({
  email: '<EMAIL>',
  password: 'password123'
})

// Get current user
const user = await authApi.getMe()

// Use helpers
const isAuth = authHelpers.isAuthenticated(user)
```

**Available Methods:**
- `login(credentials)` - User login
- `register(userData)` - User registration
- `logout()` - User logout
- `getMe()` - Get current user
- `changePassword(passwordData)` - Change password
- `updateProfile(profileData)` - Update user profile
- `uploadAvatar(file)` - Upload user avatar
- `forgotPassword(email)` - Send password reset email
- `resetPassword(resetData)` - Reset password with token
- `enable2FA()` - Enable two-factor authentication
- `verify2FA(token)` - Verify 2FA setup
- `disable2FA(password)` - Disable 2FA

**Helper Functions:**
- `authHelpers.isAuthenticated(user)` - Check if user is authenticated
- `authHelpers.hasRole(user, role)` - Check user role
- `authHelpers.hasMinimumTier(user, tier)` - Check minimum tier
- `authHelpers.getDisplayName(user)` - Get user display name
- `authHelpers.getTierLabel(tier)` - Get tier label

### 3. Upload API (`lib/api.ts`)
Handles file uploads:

```typescript
import { uploadApi } from '@/lib/api'

const formData = new FormData()
formData.append('audio', audioFile)
formData.append('title', 'Song Title')

const result = await uploadApi.uploadTrack(formData)
```

## Configuration

### Environment Variables
Create a `.env.local` file based on `.env.example`:

```bash
# API Configuration
NEXT_PUBLIC_API_URL=http://**************:5000/api

# Optional settings
NEXT_PUBLIC_API_TIMEOUT=30000
NEXT_PUBLIC_API_RETRY_ATTEMPTS=3
NEXT_PUBLIC_API_RETRY_DELAY=1000
NEXT_PUBLIC_API_DEBUG=true
```

### API Client Configuration
The centralized API client (`lib/api-config.ts`) provides:

```typescript
export const API_CONFIG = {
  BASE_URL: getApiBaseUrl(), // Environment-based
  TIMEOUT: 30000,            // 30 seconds
  RETRY_ATTEMPTS: 3,         // Retry failed requests
  RETRY_DELAY: 1000,         // 1 second base delay
}
```

### Request Features
All requests automatically include:
- `credentials: 'include'` for cookie-based authentication
- `Content-Type: 'application/json'` for JSON requests
- Automatic retry with exponential backoff
- Request/response logging (in development)
- Timeout handling
- Comprehensive error handling

## Types

### Song Types (Updated for Backend)
```typescript
interface Song {
  id: number
  songName: string
  artistName: string
  fileUrl: string
  imageUrl?: string
  genre?: string
  mood?: string
  tempo?: number
  duration: number
  pitch?: number
  tier: "FREE" | "PRO" | "BUSINESS"
  createdAt: string
  updatedAt: string
  createdBy?: number | null
  updatedBy?: number | null
  // Additional frontend properties
  isLiked?: boolean
  waveformData?: number[]
}

// Backend response structure
interface SongListResponse {
  items: Song[]
  total: number
}
```

### User Types
```typescript
interface User {
  id: string
  email: string
  name?: string
  role?: string
  tier?: 0 | 1 | 2
  avatar?: string
  createdAt?: string
  updatedAt?: string
}
```

### API Response Types
```typescript
interface ApiResponse<T> {
  data: T
  message?: string
  success: boolean
}

interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}
```

## Usage Examples

### Homepage with Real API Data
The main homepage (`app/page.tsx`) now loads real song data from the API:

```typescript
// Homepage automatically loads songs on mount
const response = await songApi.searchSongs({ limit: 50 })
const tracks = response.items.map(convertSongToTrack)
```

**Authentication Flow:**
- **Browse & Play**: No login required - users can browse and play songs freely
- **Download**: Login required - shows login modal when user tries to download
- **Premium Features**: Login required for tier-based features

**Login Modal:**
- Appears when user clicks download without being logged in
- Appears when user clicks "Log in" in header
- Seamless UX without page redirects

### Song Search Component
```typescript
import { SongSearch } from '@/components/song-search'

export function MusicPage() {
  return (
    <div>
      <h1>Music Library</h1>
      <SongSearch />
    </div>
  )
}
```

### Test Pages
- **Main Homepage**: `/` - Full music player with real API data
- **Test Homepage**: `/test-home` - Simple grid view for testing API
- **Song Demo**: Use `<SongDemo />` component for API testing

### Authentication Guard
```typescript
import { AuthGuard } from '@/lib/auth-guard'

export function ProtectedPage() {
  return (
    <AuthGuard requireAuth={true} requireTier={1}>
      <div>This content requires Pro tier</div>
    </AuthGuard>
  )
}
```

## Error Handling

All API functions include comprehensive error handling:

```typescript
try {
  const songs = await songApi.getSongByName('phat')
  console.log('Success:', songs)
} catch (error) {
  console.error('API Error:', error.message)
  // Handle error appropriately
}
```

## Migration Notes

If you're updating from the old API structure:

1. **Auth API**: Import from `@/lib/auth-api` instead of `@/lib/api`
2. **Login method**: Now takes an object `{ email, password }` instead of separate parameters
3. **User interface**: Updated to match the new auth-api types
4. **Song API**: Remains in `@/lib/api` with enhanced functionality

## Best Practices

1. **Always handle errors** with try-catch blocks
2. **Use TypeScript types** for better development experience
3. **Leverage auth helpers** for permission checks
4. **Use the search component** as a reference for implementing custom search interfaces
5. **Check examples** in the `examples/` directory for implementation patterns
