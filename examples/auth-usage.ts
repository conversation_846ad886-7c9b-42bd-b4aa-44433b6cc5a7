// Examples of how to use the Auth API functions
import { authApi, authHelpers, type User } from '@/lib/auth-api'

// Example 1: User login
export async function loginExample() {
  try {
    const response = await authApi.login({
      email: '<EMAIL>',
      password: 'password123'
    })
    
    if (response.success) {
      console.log('Login successful:', response.data.user)
      console.log('Token:', response.data.token)
      return response.data.user
    } else {
      console.log('Login failed:', response.message)
      return null
    }
  } catch (error) {
    console.error('Login error:', error)
    throw error
  }
}

// Example 2: User registration
export async function registerExample() {
  try {
    const response = await authApi.register({
      email: '<EMAIL>',
      password: 'securepassword123',
      name: '<PERSON>',
      confirmPassword: 'securepassword123'
    })
    
    if (response.success) {
      console.log('Registration successful:', response.data.user)
      return response.data.user
    } else {
      console.log('Registration failed:', response.message)
      return null
    }
  } catch (error) {
    console.error('Registration error:', error)
    throw error
  }
}

// Example 3: Get current user
export async function getCurrentUser() {
  try {
    const response = await authApi.getMe()
    
    if (response.success) {
      console.log('Current user:', response.data.user)
      return response.data.user
    } else {
      console.log('Not authenticated')
      return null
    }
  } catch (error) {
    console.error('Error getting current user:', error)
    throw error
  }
}

// Example 4: Logout
export async function logoutExample() {
  try {
    const response = await authApi.logout()
    
    if (response.success) {
      console.log('Logout successful')
      return true
    } else {
      console.log('Logout failed:', response.message)
      return false
    }
  } catch (error) {
    console.error('Logout error:', error)
    throw error
  }
}

// Example 5: Change password
export async function changePasswordExample() {
  try {
    const response = await authApi.changePassword({
      currentPassword: 'oldpassword123',
      newPassword: 'newpassword123',
      confirmPassword: 'newpassword123'
    })
    
    if (response.success) {
      console.log('Password changed successfully')
      return true
    } else {
      console.log('Password change failed:', response.message)
      return false
    }
  } catch (error) {
    console.error('Password change error:', error)
    throw error
  }
}

// Example 6: Update profile
export async function updateProfileExample() {
  try {
    const response = await authApi.updateProfile({
      name: 'John Smith',
      email: '<EMAIL>'
    })
    
    if (response.success) {
      console.log('Profile updated:', response.data.user)
      return response.data.user
    } else {
      console.log('Profile update failed:', response.message)
      return null
    }
  } catch (error) {
    console.error('Profile update error:', error)
    throw error
  }
}

// Example 7: Upload avatar
export async function uploadAvatarExample(avatarFile: File) {
  try {
    const response = await authApi.uploadAvatar(avatarFile)
    
    if (response.success) {
      console.log('Avatar uploaded:', response.data.user)
      return response.data.user
    } else {
      console.log('Avatar upload failed:', response.message)
      return null
    }
  } catch (error) {
    console.error('Avatar upload error:', error)
    throw error
  }
}

// Example 8: Forgot password
export async function forgotPasswordExample() {
  try {
    const response = await authApi.forgotPassword({
      email: '<EMAIL>'
    })
    
    if (response.success) {
      console.log('Password reset email sent')
      return true
    } else {
      console.log('Failed to send reset email:', response.message)
      return false
    }
  } catch (error) {
    console.error('Forgot password error:', error)
    throw error
  }
}

// Example 9: Reset password
export async function resetPasswordExample() {
  try {
    const response = await authApi.resetPassword({
      token: 'reset-token-from-email',
      newPassword: 'newpassword123',
      confirmPassword: 'newpassword123'
    })
    
    if (response.success) {
      console.log('Password reset successful')
      return true
    } else {
      console.log('Password reset failed:', response.message)
      return false
    }
  } catch (error) {
    console.error('Password reset error:', error)
    throw error
  }
}

// Example 10: Enable 2FA
export async function enable2FAExample() {
  try {
    const response = await authApi.enable2FA()
    
    if (response.success) {
      console.log('2FA setup:', response.data)
      console.log('QR Code:', response.data.qrCode)
      console.log('Secret:', response.data.secret)
      return response.data
    } else {
      console.log('2FA setup failed:', response.message)
      return null
    }
  } catch (error) {
    console.error('2FA setup error:', error)
    throw error
  }
}

// Example 11: Using auth helpers
export function authHelpersExample(user: User | null) {
  // Check if user is authenticated
  const isAuth = authHelpers.isAuthenticated(user)
  console.log('Is authenticated:', isAuth)
  
  // Check user role
  const isAdmin = authHelpers.hasRole(user, 'admin')
  console.log('Is admin:', isAdmin)
  
  // Check minimum tier
  const hasProTier = authHelpers.hasMinimumTier(user, 1)
  console.log('Has Pro tier or higher:', hasProTier)
  
  // Get display name
  const displayName = authHelpers.getDisplayName(user)
  console.log('Display name:', displayName)
  
  // Get tier label
  const tierLabel = authHelpers.getTierLabel(user?.tier)
  console.log('Tier label:', tierLabel)
  
  return {
    isAuth,
    isAdmin,
    hasProTier,
    displayName,
    tierLabel
  }
}

// Example 12: Complete authentication flow
export async function completeAuthFlow() {
  try {
    // 1. Check if user is already authenticated
    let user = await getCurrentUser()
    
    if (!user) {
      // 2. If not authenticated, try to login
      user = await loginExample()
    }
    
    if (user) {
      // 3. Use auth helpers to check permissions
      const authInfo = authHelpersExample(user)
      
      // 4. Update profile if needed
      if (!user.name) {
        user = await updateProfileExample()
      }
      
      console.log('Authentication flow completed:', { user, authInfo })
      return { user, authInfo }
    } else {
      console.log('Authentication failed')
      return null
    }
  } catch (error) {
    console.error('Authentication flow error:', error)
    throw error
  }
}

// Example usage in a React component:
/*
import { useEffect, useState } from 'react'
import { getCurrentUser, loginExample } from './auth-usage'
import { authHelpers, type User } from '@/lib/auth-api'

export function AuthComponent() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const currentUser = await getCurrentUser()
        setUser(currentUser)
      } catch (error) {
        console.error('Auth check failed:', error)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [])

  const handleLogin = async (email: string, password: string) => {
    try {
      const response = await authApi.login({ email, password })
      if (response.success) {
        setUser(response.data.user)
      }
    } catch (error) {
      console.error('Login failed:', error)
    }
  }

  if (loading) return <div>Loading...</div>

  if (!authHelpers.isAuthenticated(user)) {
    return <LoginForm onLogin={handleLogin} />
  }

  return (
    <div>
      <h1>Welcome, {authHelpers.getDisplayName(user)}!</h1>
      <p>Tier: {authHelpers.getTierLabel(user?.tier)}</p>
      {authHelpers.hasRole(user, 'admin') && (
        <AdminPanel />
      )}
    </div>
  )
}
*/
