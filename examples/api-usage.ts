// Examples of how to use the Song API functions
import { songApi, uploadApi } from '@/lib/api'
import { authApi } from '@/lib/auth-api'

// Example 1: Search for a specific song by name (as shown in your API documentation)
export async function searchSongByName() {
  try {
    const response = await songApi.getSongByName('Havana')

    console.log('Found songs:', response.items)
    console.log('Total songs:', response.total)
    return response.items
  } catch (error) {
    console.error('Error searching for song:', error)
    throw error
  }
}

// Example 2: Advanced search with multiple parameters
export async function advancedSongSearch() {
  try {
    const response = await songApi.advancedSearch({
      search: 'love',
      genre: 'pop',
      mood: 'happy',
      sort: 'title',
      order: 'asc',
      page: 1,
      limit: 10
    })
    
    console.log('Search results:', response.data)
    console.log('Total pages:', response.totalPages)
    console.log('Total songs:', response.total)
    
    return response
  } catch (error) {
    console.error('Error in advanced search:', error)
    throw error
  }
}

// Example 3: Get songs by genre
export async function getSongsByGenre(genre: string) {
  try {
    const response = await songApi.getSongsByGenre(genre, 1, 20)
    
    console.log(`Songs in ${genre} genre:`, response.data)
    return response.data
  } catch (error) {
    console.error(`Error getting ${genre} songs:`, error)
    throw error
  }
}

// Example 4: Get songs by mood
export async function getSongsByMood(mood: string) {
  try {
    const response = await songApi.getSongsByMood(mood, 1, 20)

    console.log(`Songs with ${mood} mood:`, response.data)
    return response.data
  } catch (error) {
    console.error(`Error getting ${mood} songs:`, error)
    throw error
  }
}

// Example 5: Get songs by artist
export async function getSongsByArtist(artistName: string) {
  try {
    const response = await songApi.getSongsByArtist(artistName, 1, 20)

    console.log(`Songs by ${artistName}:`, response.data)
    return response.data
  } catch (error) {
    console.error(`Error getting songs by ${artistName}:`, error)
    throw error
  }
}

// Example: Working with the actual backend response structure
export async function getActualSongData() {
  try {
    // Direct backend response
    const backendResponse = await songApi.searchSongs({ limit: 10 })
    console.log('Backend response:', backendResponse)
    console.log('Songs:', backendResponse.items)
    console.log('Total:', backendResponse.total)

    // Each song has this structure:
    backendResponse.items.forEach(song => {
      console.log(`Song: ${song.songName} by ${song.artistName}`)
      console.log(`Genre: ${song.genre}, Mood: ${song.mood}`)
      console.log(`Duration: ${song.duration}s, Tempo: ${song.tempo} BPM`)
      console.log(`Tier: ${song.tier}, Pitch: ${song.pitch}`)
      console.log(`Audio: ${song.fileUrl}`)
      console.log(`Image: ${song.imageUrl}`)
      console.log('---')
    })

    return backendResponse.items
  } catch (error) {
    console.error('Error getting song data:', error)
    throw error
  }
}

// Example 6: General search with pagination
export async function searchSongsWithPagination(searchTerm: string, page: number = 1) {
  try {
    const response = await songApi.searchSongs({
      search: searchTerm,
      page: page,
      limit: 20,
      sort: 'createdAt',
      order: 'desc'
    })
    
    console.log(`Page ${page} results for "${searchTerm}":`, response.data)
    console.log(`Showing ${response.data.length} of ${response.total} total results`)
    
    return response
  } catch (error) {
    console.error('Error in paginated search:', error)
    throw error
  }
}

// Example 7: Authentication examples
export async function loginExample() {
  try {
    const response = await authApi.login({
      email: '<EMAIL>',
      password: 'password123'
    })
    
    if (response.success) {
      console.log('Login successful:', response.data.user)
      return response.data.user
    } else {
      console.log('Login failed:', response.message)
      return null
    }
  } catch (error) {
    console.error('Login error:', error)
    throw error
  }
}

// Example 8: Get current user
export async function getCurrentUser() {
  try {
    const response = await authApi.getMe()
    
    if (response.success) {
      console.log('Current user:', response.data.user)
      return response.data.user
    } else {
      console.log('Not authenticated')
      return null
    }
  } catch (error) {
    console.error('Error getting current user:', error)
    throw error
  }
}

// Example 9: Upload track
export async function uploadTrackExample(audioFile: File, metadata: any) {
  try {
    const formData = new FormData()
    formData.append('audio', audioFile)
    
    // Add metadata
    Object.entries(metadata).forEach(([key, value]) => {
      formData.append(key, value as string)
    })
    
    const response = await uploadApi.uploadTrack(formData)
    
    if (response.success) {
      console.log('Upload successful:', response.data)
      return response.data
    } else {
      console.log('Upload failed:', response.message)
      return null
    }
  } catch (error) {
    console.error('Upload error:', error)
    throw error
  }
}

// Example 10: Complex search combining multiple filters
export async function complexSearch() {
  try {
    // Search for electronic songs with happy mood, sorted by tempo
    const response = await songApi.searchSongs({
      genre: 'electronic',
      mood: 'happy',
      sort: 'tempo',
      order: 'desc',
      page: 1,
      limit: 50
    })
    
    console.log('Complex search results:', response.data)
    
    // Filter results further in JavaScript if needed
    const highTempoSongs = response.data.filter(song => song.tempo && song.tempo > 120)
    console.log('High tempo songs:', highTempoSongs)
    
    return {
      allResults: response.data,
      highTempoSongs,
      totalPages: response.totalPages
    }
  } catch (error) {
    console.error('Error in complex search:', error)
    throw error
  }
}

// Example usage in a React component:
/*
import { useEffect, useState } from 'react'
import { searchSongByName, getSongsByGenre } from './api-usage'

export function MyMusicComponent() {
  const [songs, setSongs] = useState([])
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const loadSongs = async () => {
      setLoading(true)
      try {
        // Search for a specific song
        const phatSongs = await searchSongByName()
        
        // Or get songs by genre
        const popSongs = await getSongsByGenre('pop')
        
        setSongs([...phatSongs, ...popSongs])
      } catch (error) {
        console.error('Failed to load songs:', error)
      } finally {
        setLoading(false)
      }
    }

    loadSongs()
  }, [])

  if (loading) return <div>Loading...</div>

  return (
    <div>
      {songs.map(song => (
        <div key={song.id}>
          <h3>{song.title}</h3>
          <p>by {song.artist}</p>
        </div>
      ))}
    </div>
  )
}
*/
