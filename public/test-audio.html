<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WaveSurfer.js Test</title>
    <script src="https://unpkg.com/wavesurfer.js@7.9.9/dist/wavesurfer.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #000;
            color: #fff;
        }
        #waveform {
            margin: 20px 0;
            border: 1px solid #333;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            background: #22c55e;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #16a34a;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .volume-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        input[type="range"] {
            width: 100px;
        }
        .info {
            margin: 20px 0;
            padding: 10px;
            background: #333;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>WaveSurfer.js Audio Player Test</h1>
    
    <div class="info">
        <p><strong>Instructions:</strong></p>
        <ul>
            <li>Click "Load Sample Audio" to load a test audio file</li>
            <li>Use the controls to play, pause, and navigate</li>
            <li>Click on the waveform to seek to a specific position</li>
            <li>Adjust volume with the slider</li>
        </ul>
    </div>

    <div id="waveform"></div>
    
    <div class="controls">
        <button id="load-btn">Load Sample Audio</button>
        <button id="play-pause-btn" disabled>Play</button>
        <button id="prev-btn" disabled>Previous</button>
        <button id="next-btn" disabled>Next</button>
        <button id="download-btn" disabled>Download</button>
    </div>
    
    <div class="volume-control">
        <label for="volume">Volume:</label>
        <input type="range" id="volume" min="0" max="100" value="75">
        <span id="volume-value">75%</span>
    </div>
    
    <div class="info">
        <div>Current Time: <span id="current-time">0:00</span></div>
        <div>Duration: <span id="duration">0:00</span></div>
        <div>Status: <span id="status">Not loaded</span></div>
    </div>

    <script>
        let wavesurfer = null;
        let isPlaying = false;
        let currentTrackIndex = 0;
        
        // Sample audio URLs (using online samples for testing)
        const sampleTracks = [
            'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
            'https://www.soundjay.com/misc/sounds/fail-buzzer-02.wav',
            'https://www.soundjay.com/misc/sounds/success-fanfare-trumpets.wav'
        ];

        // Initialize WaveSurfer
        function initWaveSurfer() {
            if (wavesurfer) {
                wavesurfer.destroy();
            }

            wavesurfer = WaveSurfer.create({
                container: '#waveform',
                waveColor: '#374151',
                progressColor: '#22c55e',
                cursorColor: '#22c55e',
                barWidth: 2,
                barRadius: 3,
                height: 64,
                normalize: true,
                mediaControls: false,
            });

            // Event listeners
            wavesurfer.on('ready', () => {
                document.getElementById('duration').textContent = formatTime(wavesurfer.getDuration());
                document.getElementById('status').textContent = 'Ready';
                document.getElementById('play-pause-btn').disabled = false;
                document.getElementById('prev-btn').disabled = false;
                document.getElementById('next-btn').disabled = false;
                document.getElementById('download-btn').disabled = false;
                
                // Set initial volume
                const volume = document.getElementById('volume').value;
                wavesurfer.setVolume(volume / 100);
            });

            wavesurfer.on('audioprocess', () => {
                document.getElementById('current-time').textContent = formatTime(wavesurfer.getCurrentTime());
            });

            wavesurfer.on('seeking', () => {
                document.getElementById('current-time').textContent = formatTime(wavesurfer.getCurrentTime());
            });

            wavesurfer.on('finish', () => {
                handleNext();
            });

            wavesurfer.on('play', () => {
                isPlaying = true;
                document.getElementById('play-pause-btn').textContent = 'Pause';
                document.getElementById('status').textContent = 'Playing';
            });

            wavesurfer.on('pause', () => {
                isPlaying = false;
                document.getElementById('play-pause-btn').textContent = 'Play';
                document.getElementById('status').textContent = 'Paused';
            });

            wavesurfer.on('error', (error) => {
                console.error('WaveSurfer error:', error);
                document.getElementById('status').textContent = 'Error loading audio';
            });
        }

        // Format time in MM:SS
        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins}:${secs.toString().padStart(2, '0')}`;
        }

        // Load audio
        function loadAudio(url) {
            if (!wavesurfer) {
                initWaveSurfer();
            }
            
            document.getElementById('status').textContent = 'Loading...';
            wavesurfer.load(url);
        }

        // Play/Pause
        function handlePlayPause() {
            if (wavesurfer) {
                wavesurfer.playPause();
            }
        }

        // Previous track
        function handlePrevious() {
            currentTrackIndex = currentTrackIndex === 0 ? sampleTracks.length - 1 : currentTrackIndex - 1;
            loadAudio(sampleTracks[currentTrackIndex]);
        }

        // Next track
        function handleNext() {
            currentTrackIndex = (currentTrackIndex + 1) % sampleTracks.length;
            loadAudio(sampleTracks[currentTrackIndex]);
        }

        // Download
        function handleDownload() {
            if (wavesurfer && wavesurfer.getMediaElement()) {
                const audio = wavesurfer.getMediaElement();
                const a = document.createElement('a');
                a.href = audio.src;
                a.download = `track-${currentTrackIndex + 1}.wav`;
                a.click();
            }
        }

        // Volume change
        function handleVolumeChange(volume) {
            if (wavesurfer) {
                wavesurfer.setVolume(volume / 100);
            }
            document.getElementById('volume-value').textContent = volume + '%';
        }

        // Event listeners
        document.getElementById('load-btn').addEventListener('click', () => {
            loadAudio(sampleTracks[currentTrackIndex]);
        });

        document.getElementById('play-pause-btn').addEventListener('click', handlePlayPause);
        document.getElementById('prev-btn').addEventListener('click', handlePrevious);
        document.getElementById('next-btn').addEventListener('click', handleNext);
        document.getElementById('download-btn').addEventListener('click', handleDownload);

        document.getElementById('volume').addEventListener('input', (e) => {
            handleVolumeChange(e.target.value);
        });

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('WaveSurfer.js test page loaded');
        });
    </script>
</body>
</html>
