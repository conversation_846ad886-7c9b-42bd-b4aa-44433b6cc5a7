"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { songApi } from "@/lib/api"
import { simpleSong<PERSON>pi, testSimpleApi } from "@/lib/simple-api"
import { CheckCircle, XCircle, Clock } from "lucide-react"

interface TestResult {
  method: string
  status: 'pending' | 'success' | 'error'
  message: string
  data?: any
  duration?: number
}

export default function CompareApiPage() {
  const [results, setResults] = useState<TestResult[]>([])
  const [testing, setTesting] = useState(false)

  const runAllTests = async () => {
    setTesting(true)
    setResults([])

    const tests: TestResult[] = [
      { method: 'Direct Fetch', status: 'pending', message: 'Starting...' },
      { method: 'Simple API', status: 'pending', message: 'Starting...' },
      { method: 'Main API', status: 'pending', message: 'Starting...' },
      { method: 'Browser Direct', status: 'pending', message: 'Starting...' }
    ]

    setResults([...tests])

    // Test 1: Direct fetch
    const startTime1 = Date.now()
    try {
      const response = await fetch('http://**************:5000/api/song?limit=1', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        mode: 'cors',
        credentials: 'omit'
      })
      
      if (response.ok) {
        const data = await response.json()
        tests[0] = {
          method: 'Direct Fetch',
          status: 'success',
          message: `Success! Found ${data.total || data.items?.length || 0} songs`,
          data,
          duration: Date.now() - startTime1
        }
      } else {
        tests[0] = {
          method: 'Direct Fetch',
          status: 'error',
          message: `HTTP ${response.status}: ${response.statusText}`,
          duration: Date.now() - startTime1
        }
      }
    } catch (error: any) {
      tests[0] = {
        method: 'Direct Fetch',
        status: 'error',
        message: `Error: ${error.message}`,
        duration: Date.now() - startTime1
      }
    }
    setResults([...tests])

    // Test 2: Simple API
    const startTime2 = Date.now()
    try {
      const result = await testSimpleApi()
      tests[1] = {
        method: 'Simple API',
        status: result.success ? 'success' : 'error',
        message: result.success ? `Success! Multiple tests passed` : `Error: ${result.error}`,
        data: result,
        duration: Date.now() - startTime2
      }
    } catch (error: any) {
      tests[1] = {
        method: 'Simple API',
        status: 'error',
        message: `Error: ${error.message}`,
        duration: Date.now() - startTime2
      }
    }
    setResults([...tests])

    // Test 3: Main API
    const startTime3 = Date.now()
    try {
      const response = await songApi.searchSongs({ limit: 1 })
      tests[2] = {
        method: 'Main API',
        status: 'success',
        message: `Success! Found ${response.total || response.items?.length || 0} songs`,
        data: response,
        duration: Date.now() - startTime3
      }
    } catch (error: any) {
      tests[2] = {
        method: 'Main API',
        status: 'error',
        message: `Error: ${error.message}`,
        duration: Date.now() - startTime3
      }
    }
    setResults([...tests])

    // Test 4: Browser direct (just check if URL is accessible)
    const startTime4 = Date.now()
    tests[3] = {
      method: 'Browser Direct',
      status: 'success',
      message: 'Click the link below to test manually',
      duration: Date.now() - startTime4
    }
    setResults([...tests])

    setTesting(false)
  }

  const getStatusIcon = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />
    }
  }

  const getStatusBadge = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return <Badge className="bg-green-100 text-green-800">Success</Badge>
      case 'error':
        return <Badge className="bg-red-100 text-red-800">Error</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">API Method Comparison</h1>
              <p className="text-gray-600">
                Test different ways to call the API: <code className="bg-gray-200 px-2 py-1 rounded">http://**************:5000/api/song</code>
              </p>
            </div>
            <Button 
              onClick={runAllTests} 
              disabled={testing}
              size="lg"
            >
              {testing ? 'Testing...' : 'Run All Tests'}
            </Button>
          </div>
        </div>

        {/* Results */}
        <div className="grid gap-6">
          {results.map((result, index) => (
            <Card key={index}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result.status)}
                    <CardTitle>{result.method}</CardTitle>
                  </div>
                  <div className="flex items-center gap-2">
                    {result.duration && (
                      <span className="text-sm text-gray-500">{result.duration}ms</span>
                    )}
                    {getStatusBadge(result.status)}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 mb-3">{result.message}</p>
                
                {result.method === 'Browser Direct' && (
                  <div className="p-3 bg-blue-50 rounded border">
                    <p className="text-sm mb-2">Test manually by opening this URL:</p>
                    <a 
                      href="http://**************:5000/api/song" 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline break-all"
                    >
                      http://**************:5000/api/song
                    </a>
                  </div>
                )}

                {result.data && result.status === 'success' && (
                  <details className="mt-3">
                    <summary className="cursor-pointer text-blue-600 hover:text-blue-800 text-sm">
                      View Response Data
                    </summary>
                    <pre className="text-xs bg-gray-100 p-3 rounded mt-2 overflow-auto max-h-64">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Instructions */}
        {results.length > 0 && (
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Analysis & Next Steps</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="p-3 bg-green-50 rounded border-l-4 border-green-400">
                  <strong>If Direct Fetch works:</strong> The API is accessible and the issue is in the complex API client.
                </div>
                <div className="p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                  <strong>If Simple API works:</strong> Use the simple API as a fallback in your main application.
                </div>
                <div className="p-3 bg-yellow-50 rounded border-l-4 border-yellow-400">
                  <strong>If Main API fails:</strong> There might be issues with the complex error handling or retry logic.
                </div>
                <div className="p-3 bg-red-50 rounded border-l-4 border-red-400">
                  <strong>If all fail:</strong> Check CORS settings on the backend or network connectivity.
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions */}
        <Card className="mt-6">
          <CardContent className="pt-6">
            <div className="flex gap-4 justify-center">
              <Button 
                variant="outline" 
                onClick={() => window.open('/', '_blank')}
              >
                Test Homepage
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.open('/debug', '_blank')}
              >
                Debug Tools
              </Button>
              <Button 
                variant="outline" 
                onClick={() => window.open('/api-test', '_blank')}
              >
                API Test Page
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
