"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function ApiTestPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const testDirectFetch = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      console.log('Testing direct fetch to API...')
      
      const response = await fetch('http://157.10.199.189:5000/api/song', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        mode: 'cors', // Explicitly set CORS mode
        credentials: 'omit' // Try without credentials first
      })

      console.log('Response status:', response.status)
      console.log('Response ok:', response.ok)
      console.log('Response headers:', Object.fromEntries(response.headers.entries()))

      if (response.ok) {
        const data = await response.json()
        console.log('Response data:', data)
        setResult(data)
      } else {
        const errorText = await response.text()
        console.log('Error response:', errorText)
        setError(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (err: any) {
      console.error('Fetch error:', err)
      setError(`Fetch failed: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const testWithCredentials = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      console.log('Testing fetch with credentials...')
      
      const response = await fetch('http://157.10.199.189:5000/api/song', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        mode: 'cors',
        credentials: 'include' // Include credentials
      })

      console.log('Response status:', response.status)
      console.log('Response ok:', response.ok)

      if (response.ok) {
        const data = await response.json()
        console.log('Response data:', data)
        setResult(data)
      } else {
        const errorText = await response.text()
        console.log('Error response:', errorText)
        setError(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (err: any) {
      console.error('Fetch error:', err)
      setError(`Fetch failed: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  const testCorsPreflightManually = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      console.log('Testing CORS preflight manually...')
      
      // First, test OPTIONS request (CORS preflight)
      const optionsResponse = await fetch('http://157.10.199.189:5000/api/song', {
        method: 'OPTIONS',
        headers: {
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type'
        }
      })

      console.log('OPTIONS response status:', optionsResponse.status)
      console.log('OPTIONS response headers:', Object.fromEntries(optionsResponse.headers.entries()))

      if (optionsResponse.ok) {
        // If OPTIONS is successful, try the actual request
        const response = await fetch('http://157.10.199.189:5000/api/song', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        })

        if (response.ok) {
          const data = await response.json()
          setResult(data)
        } else {
          setError(`GET request failed: ${response.status}`)
        }
      } else {
        setError(`CORS preflight failed: ${optionsResponse.status}`)
      }
    } catch (err: any) {
      console.error('CORS test error:', err)
      setError(`CORS test failed: ${err.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">API Connection Test</h1>
          <p className="text-gray-600">
            Direct testing of API endpoint: <code className="bg-gray-200 px-2 py-1 rounded">http://157.10.199.189:5000/api/song</code>
          </p>
        </div>

        <div className="grid gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Test Options</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button 
                  onClick={testDirectFetch} 
                  disabled={loading}
                  className="w-full"
                >
                  {loading ? 'Testing...' : 'Test Direct Fetch'}
                </Button>
                
                <Button 
                  onClick={testWithCredentials} 
                  disabled={loading}
                  variant="outline"
                  className="w-full"
                >
                  {loading ? 'Testing...' : 'Test With Credentials'}
                </Button>
                
                <Button 
                  onClick={testCorsPreflightManually} 
                  disabled={loading}
                  variant="secondary"
                  className="w-full"
                >
                  {loading ? 'Testing...' : 'Test CORS Preflight'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {error && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="pt-6">
                <div className="text-red-600">
                  <h3 className="font-semibold mb-2">Error:</h3>
                  <p>{error}</p>
                  <p className="text-sm mt-2">Check browser console for more details.</p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Success Display */}
          {result && (
            <Card className="border-green-200 bg-green-50">
              <CardContent className="pt-6">
                <div className="text-green-600">
                  <h3 className="font-semibold mb-2">Success! API Response:</h3>
                  <div className="bg-white p-4 rounded border">
                    <p className="text-sm text-gray-600 mb-2">
                      Found {result.total || result.items?.length || 0} songs
                    </p>
                    <details>
                      <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
                        View Raw Response
                      </summary>
                      <pre className="text-xs bg-gray-100 p-2 rounded mt-2 overflow-auto max-h-96">
                        {JSON.stringify(result, null, 2)}
                      </pre>
                    </details>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Troubleshooting Guide</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div className="p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                <strong>If "Test Direct Fetch" works:</strong> The API is accessible and CORS is configured correctly.
              </div>
              <div className="p-3 bg-yellow-50 rounded border-l-4 border-yellow-400">
                <strong>If you get CORS errors:</strong> The backend needs to add your domain to allowed origins.
              </div>
              <div className="p-3 bg-red-50 rounded border-l-4 border-red-400">
                <strong>If all tests fail:</strong> Check if the backend server is running and accessible.
              </div>
              <div className="p-3 bg-green-50 rounded border-l-4 border-green-400">
                <strong>Manual test:</strong> Open <a href="http://157.10.199.189:5000/api/song" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">http://157.10.199.189:5000/api/song</a> in a new tab.
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Console Logs Info */}
        <Card className="mt-6">
          <CardContent className="pt-6">
            <p className="text-sm text-gray-600">
              <strong>Note:</strong> Open browser Developer Tools (F12) and check the Console tab for detailed logs during testing.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
