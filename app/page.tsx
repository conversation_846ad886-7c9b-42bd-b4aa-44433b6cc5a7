"use client"

import { useState, useEffect, useRef } from "react"
import { Sidebar } from "@/components/sidebar"
import { Head<PERSON> } from "@/components/header"
import { MusicPlayer } from "@/components/music-player"
import { TrackList } from "@/components/track-list"
import { LoginModal } from "@/components/login-modal"
import { useAuth } from "@/lib/auth-guard"
import { songApi, type Song, getTierNumber } from "@/lib/api"
import { simpleSongApi, type SimpleSong } from "@/lib/simple-api"
import WaveSurfer from "wavesurfer.js"

// Convert backend Song to frontend Track format for compatibility
interface Track {
  id: string
  title: string
  artist: string
  album?: string
  duration: number
  tempo: number
  tone: string
  genre: string
  mood: string
  coverUrl?: string
  audioUrl: string
  tier: 0 | 1 | 2
  isLiked?: boolean
  waveformData?: number[]
}

function convertSongToTrack(song: Song): Track {
  return {
    id: song.id.toString(),
    title: song.songName,
    artist: song.artistName,
    duration: song.duration,
    tempo: song.tempo || 120, // Default tempo if not provided
    tone: song.pitch ? `${song.pitch}` : 'C', // Convert pitch to tone or default
    genre: song.genre || 'Unknown',
    mood: song.mood || 'Neutral',
    coverUrl: song.imageUrl,
    audioUrl: song.fileUrl,
    tier: getTierNumber(song.tier),
    isLiked: false, // Default value, can be updated based on user preferences
    waveformData: Array.from({ length: 3000 }, () => Math.random() * 100), // Generate mock waveform data
  }
}

function convertSimpleSongToTrack(song: SimpleSong): Track {
  return {
    id: song.id.toString(),
    title: song.songName,
    artist: song.artistName,
    duration: song.duration,
    tempo: song.tempo || 120,
    tone: song.pitch ? `${song.pitch}` : 'C',
    genre: song.genre || 'Unknown',
    mood: song.mood || 'Neutral',
    coverUrl: song.imageUrl,
    audioUrl: song.fileUrl,
    tier: song.tier === 'PRO' ? 1 : song.tier === 'BUSINESS' ? 2 : 0,
    isLiked: false,
    waveformData: Array.from({ length: 3000 }, () => Math.random() * 100),
  }
}

export default function HomePage() {
  const { user, checkAuth } = useAuth()
  const [tracks, setTracks] = useState<Track[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentTrackId, setCurrentTrackId] = useState<string | undefined>()
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [volume, setVolume] = useState(75)
  const [duration, setDuration] = useState(0)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const wavesurferRef = useRef<WaveSurfer | null>(null)

  const currentTrack = tracks.find((track) => track.id === currentTrackId)

  // Load songs from API on component mount
  useEffect(() => {
    loadSongs()
  }, [])

  const loadSongs = async () => {
    setLoading(true)
    setError(null)

    try {
      console.log('Loading songs from API...')

      // Try main API first
      try {
        const response = await songApi.searchSongs({ limit: 50 })
        console.log('Main API Response:', response)

        const convertedTracks = response.items.map(convertSongToTrack)
        setTracks(convertedTracks)

        // Auto-select first track if available
        if (convertedTracks.length > 0 && !currentTrackId) {
          setCurrentTrackId(convertedTracks[0].id)
        }

        console.log(`Successfully loaded ${convertedTracks.length} songs via main API`)
        return
      } catch (mainApiError) {
        console.warn('Main API failed, trying simple API...', mainApiError)

        // Fallback to simple API
        const response = await simpleSongApi.getSongsWithLimit(50)
        console.log('Simple API Response:', response)

        const convertedTracks = response.items.map(convertSimpleSongToTrack)
        setTracks(convertedTracks)

        // Auto-select first track if available
        if (convertedTracks.length > 0 && !currentTrackId) {
          setCurrentTrackId(convertedTracks[0].id)
        }

        console.log(`Successfully loaded ${convertedTracks.length} songs via simple API`)
      }
    } catch (err: any) {
      console.error('All API methods failed:', err)

      // Enhanced error message
      let errorMessage = 'Failed to load songs from all API methods'
      if (err.name === 'TypeError' && err.message.includes('fetch')) {
        errorMessage = 'Unable to connect to music server. Please check your internet connection and ensure the backend is running.'
      } else if (err.status === 404) {
        errorMessage = 'Music API not found. The server might be down or the endpoint changed.'
      } else if (err.status >= 500) {
        errorMessage = 'Music server is experiencing issues. Please try again later.'
      } else if (err.message) {
        errorMessage = err.message
      }

      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Initialize WaveSurfer when track changes
  useEffect(() => {
    if (!currentTrack) return

    // Destroy existing instance
    if (wavesurferRef.current) {
      wavesurferRef.current.destroy()
    }

    // Create new WaveSurfer instance
    const wavesurfer = WaveSurfer.create({
      container: '#waveform',
      waveColor: '#374151',
      progressColor: '#22c55e',
      cursorColor: '#22c55e',
      barWidth: 2,
      barRadius: 3,
      height: 64,
      normalize: true,
      mediaControls: false,
    })

    wavesurferRef.current = wavesurfer

    // Load audio
    wavesurfer.load(currentTrack.audioUrl)

    // Event listeners
    wavesurfer.on('ready', () => {
      setDuration(wavesurfer.getDuration())
      wavesurfer.setVolume(volume / 100)
    })

    wavesurfer.on('audioprocess', () => {
      setCurrentTime(wavesurfer.getCurrentTime())
    })

    wavesurfer.on('seeking', () => {
      setCurrentTime(wavesurfer.getCurrentTime())
    })

    wavesurfer.on('finish', () => {
      handleNext()
    })

    wavesurfer.on('play', () => {
      setIsPlaying(true)
    })

    wavesurfer.on('pause', () => {
      setIsPlaying(false)
    })

    return () => {
      if (wavesurfer) {
        wavesurfer.destroy()
      }
    }
  }, [currentTrack])

  // Update volume when volume state changes
  useEffect(() => {
    if (wavesurferRef.current) {
      wavesurferRef.current.setVolume(volume / 100)
    }
  }, [volume])

  const handlePlay = (trackId: string) => {
    setCurrentTrackId(trackId)
    setCurrentTime(0)
  }

  const handlePause = () => {
    if (wavesurferRef.current) {
      wavesurferRef.current.pause()
    }
  }

  const handlePlayPause = () => {
    if (wavesurferRef.current) {
      wavesurferRef.current.playPause()
    }
  }

  const handleNext = () => {
    if (!currentTrackId || tracks.length === 0) return
    const currentIndex = tracks.findIndex((track) => track.id === currentTrackId)
    const nextIndex = (currentIndex + 1) % tracks.length
    setCurrentTrackId(tracks[nextIndex].id)
    setCurrentTime(0)
  }

  const handlePrevious = () => {
    if (!currentTrackId || tracks.length === 0) return
    const currentIndex = tracks.findIndex((track) => track.id === currentTrackId)
    const prevIndex = currentIndex === 0 ? tracks.length - 1 : currentIndex - 1
    setCurrentTrackId(tracks[prevIndex].id)
    setCurrentTime(0)
  }

  const handleSeek = (time: number) => {
    if (wavesurferRef.current && duration > 0) {
      const seekPosition = time / duration
      wavesurferRef.current.seekTo(seekPosition)
    }
  }

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume)
    if (wavesurferRef.current) {
      wavesurferRef.current.setVolume(newVolume / 100)
    }
  }

  const handleDownload = async (trackId: string) => {
    // Check if user is authenticated before allowing download
    if (!user) {
      setShowLoginModal(true)
      return
    }

    const track = tracks.find(t => t.id === trackId)
    if (!track) return

    try {
      const response = await fetch(track.audioUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.style.display = 'none'
      a.href = url
      a.download = `${track.artist} - ${track.title}.mp3`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Download failed:', error)
      alert('Download failed. Please try again.')
    }
  }

  const handleLoginSuccess = () => {
    // Refresh user data after successful login
    checkAuth()
  }

  const handleAddToPlaylist = (trackId: string) => {
    console.log("Adding to playlist:", trackId)
    // Implement add to playlist logic
  }

  const handleToggleLike = (trackId: string) => {
    console.log("Toggling like for track:", trackId)
    // Implement like toggle logic
  }



  return (
      <div className="flex h-screen bg-black text-white">
        {/* Sidebar */}
        <Sidebar userRole={user?.role as "user" | "admin" | undefined} userTier={getTierNumber(user?.tier?.toString() || "FREE")} />

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <Header
            user={user ? {
              name: user.name || user.email || 'User',
              email: user.email || '',
              avatar: user.avatar,
              role: (user.role as "user" | "admin") || "user",
              tier: getTierNumber(user.tier?.toString() || "FREE")
            } : undefined}
            onLoginClick={() => setShowLoginModal(true)}
          />

          {/* Content */}
          <main className="flex-1 overflow-auto p-6 pb-36">
            <div className="max-w-7xl mx-auto">
              <div className="mb-8">
                <h1 className="text-3xl font-bold mb-2">Your Music Library</h1>
                <p className="text-gray-400">
                  {loading ? 'Loading your favorite tracks...' :
                   error ? 'Failed to load tracks' :
                   `Discover and enjoy ${tracks.length} tracks`}
                </p>
              </div>

              {/* Loading State */}
              {loading && (
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
                  <span className="ml-4 text-lg">Loading songs...</span>
                </div>
              )}

              {/* Error State */}
              {error && !loading && (
                <div className="text-center py-12">
                  <div className="text-red-400 mb-6">
                    <svg className="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <p className="text-xl font-semibold mb-2">{error}</p>
                    <p className="text-gray-500 text-sm mb-4">
                      Check the browser console for more details
                    </p>
                  </div>
                  <div className="flex gap-3 justify-center">
                    <button
                      onClick={loadSongs}
                      className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors"
                    >
                      Try Again
                    </button>
                    <button
                      onClick={() => window.open('/debug', '_blank')}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
                    >
                      Debug API
                    </button>
                  </div>

                  {/* Debug Info */}
                  <div className="mt-6 p-4 bg-gray-800 rounded-lg text-left max-w-md mx-auto">
                    <p className="text-sm text-gray-300 mb-2">Debug Information:</p>
                    <div className="text-xs text-gray-400 space-y-1">
                      <div>API URL: {songApi.searchSongs.toString().includes('157.10.199.189') ? 'http://157.10.199.189:5000/api' : 'Unknown'}</div>
                      <div>Browser: {typeof window !== 'undefined' ? window.navigator.userAgent.split(' ')[0] : 'Unknown'}</div>
                      <div>Time: {new Date().toLocaleTimeString()}</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Track List */}
              {!loading && !error && tracks.length > 0 && (
                <TrackList
                  tracks={tracks}
                  currentTrackId={currentTrackId}
                  isPlaying={isPlaying}
                  userTier={user?.tier || 0}
                  onPlay={handlePlay}
                  onPause={handlePause}
                  onDownload={handleDownload}
                  onAddToPlaylist={handleAddToPlaylist}
                  onToggleLike={handleToggleLike}
                />
              )}

              {/* Empty State */}
              {!loading && !error && tracks.length === 0 && (
                <div className="text-center py-12">
                  <div className="text-gray-400 mb-4">
                    <svg className="w-16 h-16 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM15.657 6.343a1 1 0 011.414 0A9.972 9.972 0 0119 12a9.972 9.972 0 01-1.929 5.657 1 1 0 11-1.414-1.414A7.971 7.971 0 0017 12a7.971 7.971 0 00-1.343-4.243 1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                    <p className="text-xl font-semibold">No songs available</p>
                    <p className="text-gray-500 mt-2">Check back later for new music</p>
                  </div>
                </div>
              )}
            </div>
          </main>
        </div>

        {/* Music Player */}
        {currentTrack && (
          <MusicPlayer
            currentTrack={currentTrack}
            isPlaying={isPlaying}
            onPlayPause={handlePlayPause}
            onNext={handleNext}
            onPrevious={handlePrevious}
            onSeek={handleSeek}
            currentTime={currentTime}
            volume={volume}
            onVolumeChange={handleVolumeChange}
            onDownload={handleDownload}
          />
        )}

        {/* Login Modal */}
        <LoginModal
          isOpen={showLoginModal}
          onClose={() => setShowLoginModal(false)}
          onLoginSuccess={handleLoginSuccess}
          title="Login Required"
          description="Please login to download songs and access premium features"
        />
      </div>
  )
}
