"use client"

import { useState, useEffect } from "react"
import { songApi, type Song, getTierNumber } from "@/lib/api"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Play, Clock, User, Music, RefreshCw } from "lucide-react"

export default function TestHomePage() {
  const [songs, setSongs] = useState<Song[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadSongs()
  }, [])

  const loadSongs = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await songApi.searchSongs({ limit: 20 })
      setSongs(response.items)
      console.log('Loaded songs:', response)
    } catch (err: any) {
      setError(err.message || 'Failed to load songs')
      console.error('Error loading songs:', err)
    } finally {
      setLoading(false)
    }
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'PRO':
        return 'bg-blue-100 text-blue-800'
      case 'BUSINESS':
        return 'bg-purple-100 text-purple-800'
      case 'FREE':
      default:
        return 'bg-green-100 text-green-800'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">Music Library</h1>
              <p className="text-gray-600">
                {loading ? 'Loading your music...' : 
                 error ? 'Failed to load music' :
                 `${songs.length} songs available`}
              </p>
            </div>
            <Button onClick={loadSongs} disabled={loading} className="flex items-center gap-2">
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-xl text-gray-600">Loading songs...</p>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6 text-center">
              <div className="text-red-600 mb-4">
                <Music className="h-16 w-16 mx-auto mb-4" />
                <p className="text-xl font-semibold">{error}</p>
              </div>
              <Button onClick={loadSongs} variant="outline">
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Songs Grid */}
        {!loading && !error && songs.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {songs.map((song) => (
              <Card key={song.id} className="hover:shadow-lg transition-shadow duration-200">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg font-semibold truncate">
                        {song.songName}
                      </CardTitle>
                      <p className="text-gray-600 flex items-center gap-1 mt-1">
                        <User className="h-3 w-3" />
                        <span className="truncate">{song.artistName}</span>
                      </p>
                    </div>
                    <Badge className={getTierColor(song.tier)}>
                      {song.tier}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent>
                  {/* Cover Image */}
                  {song.imageUrl && (
                    <div className="mb-4">
                      <img
                        src={song.imageUrl}
                        alt={`${song.songName} cover`}
                        className="w-full h-48 object-cover rounded-lg"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none'
                        }}
                      />
                    </div>
                  )}

                  {/* Song Details */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{formatDuration(song.duration)}</span>
                      </div>
                      {song.tempo && (
                        <span>{song.tempo} BPM</span>
                      )}
                    </div>

                    {song.genre && song.mood && (
                      <div className="flex gap-2">
                        <Badge variant="secondary" className="text-xs">
                          {song.genre}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {song.mood}
                        </Badge>
                      </div>
                    )}

                    {song.pitch && (
                      <div className="text-xs text-gray-500">
                        Pitch: {song.pitch}
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2">
                    <Button 
                      size="sm" 
                      className="flex-1"
                      onClick={() => {
                        if (song.fileUrl) {
                          window.open(song.fileUrl, '_blank')
                        }
                      }}
                    >
                      <Play className="h-4 w-4 mr-1" />
                      Play
                    </Button>
                  </div>

                  {/* Metadata */}
                  <div className="mt-4 pt-3 border-t border-gray-200">
                    <div className="text-xs text-gray-500 space-y-1">
                      <div>ID: {song.id}</div>
                      <div>Created: {new Date(song.createdAt).toLocaleDateString()}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && songs.length === 0 && (
          <Card>
            <CardContent className="pt-6 text-center py-20">
              <Music className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No songs found</h3>
              <p className="text-gray-600 mb-4">There are no songs available at the moment.</p>
              <Button onClick={loadSongs}>
                Refresh
              </Button>
            </CardContent>
          </Card>
        )}

        {/* API Info */}
        <div className="mt-12 p-4 bg-gray-100 rounded-lg">
          <h3 className="font-semibold mb-2">API Information</h3>
          <div className="text-sm text-gray-600 space-y-1">
            <div>Endpoint: GET /api/song</div>
            <div>Total songs loaded: {songs.length}</div>
            <div>Status: {loading ? 'Loading...' : error ? 'Error' : 'Success'}</div>
          </div>
        </div>
      </div>
    </div>
  )
}
