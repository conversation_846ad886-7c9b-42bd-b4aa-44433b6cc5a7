"use client"

import { Sidebar } from "@/components/sidebar"
import { <PERSON><PERSON> } from "@/components/header"
import { UploadForm } from "@/components/upload-form"
import { AuthGuard, useAuth } from "@/lib/auth-guard"

export default function UploadPage() {
  const { user } = useAuth()

  const handleUpload = async (formData: FormData) => {
    try {
      const { uploadApi } = await import("@/lib/api")
      const response = await uploadApi.uploadTrack(formData)

      if (response.success) {
        console.log("Upload successful:", response.data)
        alert("Track uploaded successfully!")
      } else {
        throw new Error(response.message || "Upload failed")
      }
    } catch (error: any) {
      console.error("Upload error:", error)
      alert(error.message || "Upload failed. Please try again.")
    }
  }

  return (
    <AuthGuard requireAuth={true}>
      <div className="flex h-screen bg-black text-white">
        {/* Sidebar */}
        <Sidebar userRole={user?.role} userTier={user?.tier} />

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <Header user={user} />

          {/* Content */}
          <main className="flex-1 overflow-auto p-6">
            <div className="max-w-4xl mx-auto">
              <div className="mb-8">
                <h1 className="text-3xl font-bold mb-2">Upload Music</h1>
                <p className="text-gray-400">
                  Share your music with the world. Our AI will automatically analyze tempo, tone, and mood.
                </p>
              </div>

              <UploadForm userTier={user?.tier || 0} onUpload={handleUpload} />
            </div>
          </main>
        </div>
      </div>
    </AuthGuard>
  )
}
