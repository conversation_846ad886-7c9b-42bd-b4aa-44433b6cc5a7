"use client"

import { ApiDebug } from "@/components/api-debug"
import { QuickApiTest } from "@/components/quick-api-test"
import Link from "next/link"

export default function DebugPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">API Debug</h1>
              <p className="text-gray-600">
                Diagnose API connection issues and test endpoints
              </p>
            </div>
            <Link
              href="/"
              className="text-blue-600 hover:text-blue-800 underline"
            >
              ← Back to Home
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          <QuickApiTest />
          <div className="lg:col-span-2">
            <div className="bg-white p-6 rounded-lg shadow">
              <h2 className="text-xl font-semibold mb-4">Common Solutions</h2>
              <div className="space-y-3 text-sm">
                <div className="p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                  <strong>CORS Error:</strong> The backend needs to allow your domain.
                  Add your frontend URL to the CORS configuration.
                </div>
                <div className="p-3 bg-yellow-50 rounded border-l-4 border-yellow-400">
                  <strong>Network Error:</strong> Check if the backend server is running on
                  <code className="mx-1 px-1 bg-gray-200 rounded">http://157.10.199.189:5000</code>
                </div>
                <div className="p-3 bg-green-50 rounded border-l-4 border-green-400">
                  <strong>Success:</strong> If the quick test passes, the issue might be in the frontend code.
                </div>
              </div>
            </div>
          </div>
        </div>

        <ApiDebug />
      </div>
    </div>
  )
}
