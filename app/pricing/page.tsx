"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, Crown, Building2, Users, ArrowLeft, Star } from "lucide-react"
import Link from "next/link"
import { AppLayout } from "@/components/app-layout"
import { useAuthStore } from "@/lib/auth-store"

export default function PricingPage() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "yearly">("yearly")
  const { user, isAuthenticated } = useAuthStore()

  const plans = [
    {
      id: "personal",
      name: "<PERSON><PERSON><PERSON>",
      description: "Hoàn hảo cho người sáng tạo cá nhân",
      icon: <Crown className="h-6 w-6" />,
      price: {
        monthly: 299000,
        yearly: 2990000,
      },
      originalPrice: {
        yearly: 3588000,
      },
      duration: "1 năm",
      popular: true,
      features: [
        "Xuất bản nội dung ở khắp mọi nơi",
        "Kiếm tiền cho 1 kênh trên mỗi nền tảng",
        "Tải xuống không giới hạn",
        "Truy cập khóa học cho người sáng tạo",
        "Hỗ trợ email 24/7",
        "Thư viện nhạc premium",
      ],
      buttonText: "Bắt đầu ngay",
      tier: 1,
    },
    {
      id: "commercial",
      name: "Gói Thương Mại",
      description: "Dành cho doanh nghiệp nhỏ và vừa",
      icon: <Building2 className="h-6 w-6" />,
      price: {
        monthly: 599000,
        yearly: 5990000,
      },
      originalPrice: {
        yearly: 7188000,
      },
      duration: "Vĩnh viễn",
      popular: false,
      features: [
        "Mọi thứ trong gói cá nhân",
        "Kiếm tiền cho 3 kênh trên mỗi nền tảng",
        "Xuất bản nội dung cho khách hàng",
        "Sử dụng để chạy quảng cáo online",
        "Hỗ trợ ưu tiên",
        "Quản lý nhiều dự án",
        "API truy cập",
      ],
      buttonText: "Nâng cấp ngay",
      tier: 2,
    },
    {
      id: "enterprise",
      name: "Gói Doanh Nghiệp Lớn",
      description: "Giải pháp tùy chỉnh cho doanh nghiệp",
      icon: <Users className="h-6 w-6" />,
      price: {
        monthly: "Liên hệ",
        yearly: "Liên hệ",
      },
      duration: "Tùy chỉnh",
      popular: false,
      features: [
        "Mọi thứ trong gói thương mại",
        "Không giới hạn kênh kiếm tiền",
        "Giải pháp tùy chỉnh",
        "Hỗ trợ dedicated account manager",
        "SLA 99.9% uptime",
        "Tích hợp enterprise",
        "Đào tạo và onboarding",
        "Báo cáo và phân tích nâng cao",
      ],
      buttonText: "Liên hệ tư vấn",
      tier: 3,
    },
  ]

  const formatPrice = (price: number | string) => {
    if (typeof price === "string") return price
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(price)
  }

  const handleSelectPlan = (planId: string, tier: number) => {
    if (!isAuthenticated) {
      // Redirect to login
      window.location.href = "/login"
      return
    }

    if (planId === "enterprise") {
      // Open contact form or redirect to contact page
      window.location.href = "/contact"
      return
    }

    // Handle plan selection logic here
    console.log(`Selected plan: ${planId}, tier: ${tier}`)
    // You can integrate with payment gateway here
  }

  return (
      <div className="min-h-screen bg-white text-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-16">
        <div className="text-center mb-8 md:mb-16">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-orange-500 mb-4 md:mb-6">
            Chọn Gói Phù Hợp Với Bạn
          </h1>
          <p className="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto mb-6 md:mb-8 px-4">
            Từ người sáng tạo cá nhân đến doanh nghiệp lớn, chúng tôi có giải pháp phù hợp cho mọi nhu cầu
          </p>

          {/* Billing Toggle */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-6 md:mb-8">
            <div className="flex items-center gap-4">
              <span className={`text-sm ${billingCycle === "monthly" ? "text-black" : "text-gray-500"}`}>
                Hàng tháng
              </span>
              <button
                onClick={() => setBillingCycle(billingCycle === "monthly" ? "yearly" : "monthly")}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  billingCycle === "yearly" ? "bg-orange-500" : "bg-gray-300"
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    billingCycle === "yearly" ? "translate-x-6" : "translate-x-1"
                  }`}
                />
              </button>
              <span className={`text-sm ${billingCycle === "yearly" ? "text-black" : "text-gray-500"}`}>
                Hàng năm
              </span>
            </div>
            {billingCycle === "yearly" && (
              <Badge className="bg-orange-100 text-orange-600 border-orange-200">
                Tiết kiệm 17%
              </Badge>
            )}
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 max-w-6xl mx-auto">
          {plans.map((plan) => (
            <Card
              key={plan.id}
              className={`relative border-2 transition-all duration-300 hover:shadow-lg ${
                plan.popular
                  ? "border-orange-500 shadow-lg scale-105"
                  : "border-gray-200 hover:border-orange-300"
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-orange-500 text-white px-4 py-1 flex items-center gap-1">
                    <Star className="h-3 w-3" />
                    Phổ biến nhất
                  </Badge>
                </div>
              )}

              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-orange-100 rounded-full text-orange-500">
                    {plan.icon}
                  </div>
                </div>
                <CardTitle className="text-2xl font-bold text-orange-500 mb-2">
                  {plan.name}
                </CardTitle>
                <CardDescription className="text-gray-600">
                  {plan.description}
                </CardDescription>
              </CardHeader>

              <CardContent className="pt-0">
                {/* Price */}
                <div className="text-center mb-6">
                  <div className="flex items-baseline justify-center gap-2">
                    <span className="text-3xl font-bold text-black">
                      {formatPrice(plan.price[billingCycle])}
                    </span>
                    {typeof plan.price[billingCycle] === "number" && (
                      <span className="text-gray-500">
                        /{billingCycle === "monthly" ? "tháng" : "năm"}
                      </span>
                    )}
                  </div>
                  {billingCycle === "yearly" && plan.originalPrice?.yearly && (
                    <div className="text-sm text-gray-500 line-through mt-1">
                      {formatPrice(plan.originalPrice.yearly)}
                    </div>
                  )}
                  <div className="text-sm text-gray-600 mt-2">
                    Thời hạn: {plan.duration}
                  </div>
                </div>

                {/* Features */}
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <Check className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* CTA Button */}
                <Button
                  onClick={() => handleSelectPlan(plan.id, plan.tier)}
                  className={`w-full py-3 ${
                    plan.popular
                      ? "bg-orange-500 hover:bg-orange-600 text-white"
                      : "bg-white border-2 border-orange-500 text-orange-500 hover:bg-orange-50"
                  }`}
                >
                  {plan.buttonText}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* FAQ or Additional Info */}
        <div className="mt-16 text-center">
          <h3 className="text-2xl font-bold text-orange-500 mb-4">
            Cần hỗ trợ thêm?
          </h3>
          <p className="text-gray-600 mb-6">
            Đội ngũ chuyên gia của chúng tôi sẵn sàng hỗ trợ bạn chọn gói phù hợp nhất
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="outline" className="border-orange-500 text-orange-500 hover:bg-orange-50">
              Liên hệ tư vấn
            </Button>
            <Button variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-50">
              Xem câu hỏi thường gặp
            </Button>
          </div>
        </div>
      </div>
      </div>
  )
}
