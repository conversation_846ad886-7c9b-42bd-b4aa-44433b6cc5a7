import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import type { User } from './auth-api'

interface AuthState {
  user: User | null
  accessToken: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

interface AuthActions {
  setUser: (user: User | null) => void
  setTokens: (accessToken: string, refreshToken: string) => void
  clearAuth: () => void
  setLoading: (loading: boolean) => void
  login: (user: User, accessToken: string, refreshToken: string) => void
  logout: () => void
}

type AuthStore = AuthState & AuthActions

const initialState: AuthState = {
  user: null,
  accessToken: null,
  refreshToken: null,
  isAuthenticated: false,
  isLoading: true,
}

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      ...initialState,

      setUser: (user) => 
        set((state) => ({ 
          user, 
          isAuthenticated: !!user 
        })),

      setTokens: (accessToken, refreshToken) =>
        set({ accessToken, refreshToken }),

      clearAuth: () =>
        set({
          user: null,
          accessToken: null,
          refreshToken: null,
          isAuthenticated: false,
        }),

      setLoading: (isLoading) =>
        set({ isLoading }),

      login: (user, accessToken, refreshToken) =>
        set({
          user,
          accessToken,
          refreshToken,
          isAuthenticated: true,
          isLoading: false,
        }),

      logout: () => {
        set({
          user: null,
          accessToken: null,
          refreshToken: null,
          isAuthenticated: false,
          isLoading: false,
        })
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        user: state.user,
        accessToken: state.accessToken,
        refreshToken: state.refreshToken,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)

// Helper functions
export const authHelpers = {
  // Check if user has specific role
  hasRole: (role: string): boolean => {
    const { user } = useAuthStore.getState()
    return user?.role === role
  },

  // Check if user has minimum tier
  hasMinimumTier: (minTier: 0 | 1 | 2): boolean => {
    const { user } = useAuthStore.getState()
    return (user?.tier ?? 0) >= minTier
  },

  // Get user display name
  getDisplayName: (): string => {
    const { user } = useAuthStore.getState()
    return user?.name || user?.email || 'Anonymous'
  },

  // Get tier label
  getTierLabel: (tier?: 0 | 1 | 2): string => {
    switch (tier) {
      case 1:
        return 'Pro'
      case 2:
        return 'Business'
      default:
        return 'Free'
    }
  },

  // Check if authenticated
  isAuthenticated: (): boolean => {
    const { isAuthenticated } = useAuthStore.getState()
    return isAuthenticated
  },

  // Get current user
  getCurrentUser: (): User | null => {
    const { user } = useAuthStore.getState()
    return user
  },

  // Get access token
  getAccessToken: (): string | null => {
    const { accessToken } = useAuthStore.getState()
    return accessToken
  },

  // Get refresh token
  getRefreshToken: (): string | null => {
    const { refreshToken } = useAuthStore.getState()
    return refreshToken
  },
}
