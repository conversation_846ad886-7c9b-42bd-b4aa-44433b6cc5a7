"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

import type { User } from './auth-api'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireRole?: "user" | "admin"
  requireTier?: 0 | 1 | 2
  fallback?: React.ReactNode
}

export function AuthGuard({ children, requireAuth = true, requireRole, requireTier, fallback }: AuthGuardProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const { authApi } = await import('./auth-api')
      const response = await authApi.getMe()

      if (response.success && response.data.user) {
        setUser(response.data.user)
      } else {
        setUser(null)
        if (requireAuth) {
          router.push("/login")
          return
        }
      }
    } catch (error) {
      console.error("Auth check failed:", error)
      setUser(null)
      if (requireAuth) {
        router.push("/login")
        return
      }
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-black text-white">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
      </div>
    )
  }

  if (requireAuth && !user) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-black text-white">
          <p>Redirecting to login...</p>
        </div>
      )
    )
  }

  if (requireRole && user?.role !== requireRole) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-black text-white">
          <p>Access denied. Insufficient permissions.</p>
        </div>
      )
    )
  }

  if (requireTier !== undefined && user && (user.tier ?? 0) < requireTier) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-black text-white">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Upgrade Required</h2>
            <p className="text-gray-400 mb-6">This feature requires a higher subscription tier.</p>
            <button
              onClick={() => router.push("/pricing")}
              className="bg-green-500 hover:bg-green-600 text-black px-6 py-2 rounded-lg font-medium"
            >
              View Plans
            </button>
          </div>
        </div>
      )
    )
  }

  return <>{children}</>
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const { authApi } = await import('./auth-api')
      const response = await authApi.getMe()

      if (response.success && response.data.user) {
        setUser(response.data.user)
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error("Auth check failed:", error)
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    try {
      const { authApi } = await import('./auth-api')
      const response = await authApi.login({ email, password })

      if (response.success && response.data.user) {
        setUser(response.data.user)
        return { success: true }
      } else {
        return { success: false, error: response.message || "Login failed" }
      }
    } catch (error: any) {
      return { success: false, error: error.message || "Login failed" }
    }
  }

  const logout = async () => {
    try {
      const { authApi } = await import('./auth-api')
      await authApi.logout()
      setUser(null)
    } catch (error) {
      console.error("Logout failed:", error)
    }
  }

  return { user, loading, login, logout, checkAuth }
}
