"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

import type { User } from './auth-api'
import { useAuthStore } from './auth-store'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireRole?: "user" | "admin"
  requireTier?: 0 | 1 | 2
  fallback?: React.ReactNode
}

export function AuthGuard({ children, requireAuth = true, requireRole, requireTier, fallback }: AuthGuardProps) {
  const { user, isLoading, setLoading } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    // Set loading to false after initial mount
    // Zustand will handle loading the persisted state
    setLoading(false)

    if (requireAuth && !user) {
      router.push("/login")
    }
  }, [user, requireAuth, router, setLoading])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-white text-black">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    )
  }

  if (requireAuth && !user) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-white text-black">
          <p>Redirecting to login...</p>
        </div>
      )
    )
  }

  if (requireRole && user?.role !== requireRole) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-white text-black">
          <p>Access denied. Insufficient permissions.</p>
        </div>
      )
    )
  }

  if (requireTier !== undefined && user && (user.tier ?? 0) < requireTier) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-white text-black">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4 text-orange-500">Upgrade Required</h2>
            <p className="text-gray-600 mb-6">This feature requires a higher subscription tier.</p>
            <button
              onClick={() => router.push("/pricing")}
              className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium"
            >
              View Plans
            </button>
          </div>
        </div>
      )
    )
  }

  return <>{children}</>
}

export function useAuth() {
  const {
    user,
    isLoading,
    login: storeLogin,
    logout: storeLogout,
    setLoading
  } = useAuthStore()

  useEffect(() => {
    // Set loading to false after initial mount
    // Zustand will handle loading the persisted state
    setLoading(false)
  }, [setLoading])

  const checkAuth = () => {
    // Auth state is managed by Zustand store
    // This function is kept for compatibility
    return Promise.resolve()
  }

  const login = async (email: string, password: string) => {
    try {
      const { authApi } = await import('./auth-api')
      const response = await authApi.login({ email, password })

      if (response && response.access_token && response.user) {
        // Convert to our User interface
        const user: User = {
          id: response.user.id.toString(),
          email: response.user.email,
          name: response.user.name,
          role: response.user.roleLst?.[0] || 'user',
          tier: (response.user.tier || 0) as 0 | 1 | 2,
          avatar: undefined,
          createdAt: response.user.createdAt,
          updatedAt: response.user.updatedAt
        }

        // Store in Zustand store
        storeLogin(user, response.access_token, response.refresh_token)
        return { success: true }
      } else {
        return { success: false, error: "Login failed - Invalid response" }
      }
    } catch (error: any) {
      return { success: false, error: error.message || "Login failed" }
    }
  }

  const logout = async () => {
    try {
      // Clear Zustand store
      storeLogout()

      // Optionally call logout API
      try {
        const { authApi } = await import('./auth-api')
        await authApi.logout()
      } catch (error) {
        console.warn("Logout API call failed:", error)
      }
    } catch (error) {
      console.error("Logout failed:", error)
    }
  }

  return { user, loading: isLoading, login, logout, checkAuth }
}
