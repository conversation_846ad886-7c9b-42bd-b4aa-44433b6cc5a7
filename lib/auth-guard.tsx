"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"

import type { User } from './auth-api'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireRole?: "user" | "admin"
  requireTier?: 0 | 1 | 2
  fallback?: React.ReactNode
}

export function AuthGuard({ children, requireAuth = true, requireRole, requireTier, fallback }: AuthGuardProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const { authApi } = await import('./auth-api')
      const response = await authApi.getMe()

      if (response.success && response.data.user) {
        setUser(response.data.user)
      } else {
        setUser(null)
        if (requireAuth) {
          router.push("/login")
          return
        }
      }
    } catch (error) {
      console.error("Auth check failed:", error)
      setUser(null)
      if (requireAuth) {
        router.push("/login")
        return
      }
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-white text-black">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
      </div>
    )
  }

  if (requireAuth && !user) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-white text-black">
          <p>Redirecting to login...</p>
        </div>
      )
    )
  }

  if (requireRole && user?.role !== requireRole) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-white text-black">
          <p>Access denied. Insufficient permissions.</p>
        </div>
      )
    )
  }

  if (requireTier !== undefined && user && (user.tier ?? 0) < requireTier) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen bg-white text-black">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4 text-orange-500">Upgrade Required</h2>
            <p className="text-gray-600 mb-6">This feature requires a higher subscription tier.</p>
            <button
              onClick={() => router.push("/pricing")}
              className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium"
            >
              View Plans
            </button>
          </div>
        </div>
      )
    )
  }

  return <>{children}</>
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      // First check localStorage for user data
      const storedUser = localStorage.getItem('user')
      const accessToken = localStorage.getItem('access_token')

      if (storedUser && accessToken) {
        const userData = JSON.parse(storedUser)
        // Convert API user format to our User interface
        const user: User = {
          id: userData.id.toString(),
          email: userData.email,
          name: userData.name,
          role: userData.roleLst?.[0] || 'user',
          tier: userData.tier || 0,
          avatar: userData.avatar,
          createdAt: userData.createdAt,
          updatedAt: userData.updatedAt
        }
        setUser(user)
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error("Auth check failed:", error)
      setUser(null)
      // Clear invalid data
      localStorage.removeItem('user')
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    } finally {
      setLoading(false)
    }
  }

  const login = async (email: string, password: string) => {
    try {
      const { authApi } = await import('./auth-api')
      const response = await authApi.login({ email, password })

      if (response && response.access_token && response.user) {
        // Store tokens and user data
        localStorage.setItem('access_token', response.access_token)
        localStorage.setItem('refresh_token', response.refresh_token)
        localStorage.setItem('user', JSON.stringify(response.user))

        // Convert to our User interface
        const user: User = {
          id: response.user.id.toString(),
          email: response.user.email,
          name: response.user.name,
          role: response.user.roleLst?.[0] || 'user',
          tier: response.user.tier || 0,
          avatar: undefined,
          createdAt: response.user.createdAt,
          updatedAt: response.user.updatedAt
        }
        setUser(user)
        return { success: true }
      } else {
        return { success: false, error: "Login failed - Invalid response" }
      }
    } catch (error: any) {
      return { success: false, error: error.message || "Login failed" }
    }
  }

  const logout = async () => {
    try {
      // Clear local storage
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user')
      setUser(null)

      // Optionally call logout API
      try {
        const { authApi } = await import('./auth-api')
        await authApi.logout()
      } catch (error) {
        console.warn("Logout API call failed:", error)
      }
    } catch (error) {
      console.error("Logout failed:", error)
    }
  }

  return { user, loading, login, logout, checkAuth }
}
