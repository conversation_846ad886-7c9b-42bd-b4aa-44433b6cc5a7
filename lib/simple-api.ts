// Simple API functions without complex error handling for testing
const API_BASE_URL = 'http://157.10.199.189:5000/api'

export interface SimpleSong {
  id: number
  songName: string
  artistName: string
  fileUrl: string
  imageUrl?: string
  genre?: string
  mood?: string
  tempo?: number
  duration: number
  pitch?: number
  tier: string
  createdAt: string
  updatedAt: string
}

export interface SimpleSongResponse {
  items: SimpleSong[]
  total: number
}

// Simple fetch function
export async function simpleFetch<T>(endpoint: string): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`
  
  console.log('Simple fetch to:', url)
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      mode: 'cors',
      credentials: 'omit' // No credentials for testing
    })

    console.log('Simple fetch response:', {
      status: response.status,
      ok: response.ok,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log('Simple fetch data:', data)
    return data
  } catch (error) {
    console.error('Simple fetch error:', error)
    throw error
  }
}

// Simple song API
export const simpleSongApi = {
  async getAllSongs(): Promise<SimpleSongResponse> {
    return simpleFetch<SimpleSongResponse>('/song')
  },

  async getSongsWithLimit(limit: number = 10): Promise<SimpleSongResponse> {
    return simpleFetch<SimpleSongResponse>(`/song?limit=${limit}`)
  },

  async getSongByName(name: string): Promise<SimpleSongResponse> {
    return simpleFetch<SimpleSongResponse>(`/song?songName=${encodeURIComponent(name)}`)
  }
}

// Test function
export async function testSimpleApi() {
  try {
    console.log('=== Testing Simple API ===')
    
    // Test 1: Get all songs
    console.log('Test 1: Get all songs')
    const allSongs = await simpleSongApi.getAllSongs()
    console.log('All songs result:', allSongs)
    
    // Test 2: Get limited songs
    console.log('Test 2: Get limited songs')
    const limitedSongs = await simpleSongApi.getSongsWithLimit(5)
    console.log('Limited songs result:', limitedSongs)
    
    // Test 3: Search by name
    console.log('Test 3: Search by name')
    const searchResult = await simpleSongApi.getSongByName('Havana')
    console.log('Search result:', searchResult)
    
    return {
      success: true,
      allSongs,
      limitedSongs,
      searchResult
    }
  } catch (error) {
    console.error('Simple API test failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
