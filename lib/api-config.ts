// API Configuration and HTTP Client
// Centralized API configuration for the application

// Environment-based configuration
const getApiBaseUrl = (): string => {
  // Check if we're in development, staging, or production
  if (typeof window !== 'undefined') {
    // Client-side
    const hostname = window.location.hostname
    
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return process.env.NEXT_PUBLIC_API_URL || 'http://**************:5000/api'
    }
  }
  
  // Server-side or production
  return process.env.NEXT_PUBLIC_API_URL || 'http://**************:5000/api'
}

// API Configuration
export const API_CONFIG = {
  BASE_URL: getApiBaseUrl(),
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
} as const

// Default headers
const getDefaultHeaders = (): HeadersInit => ({
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  // Add any other default headers here
})

// Request configuration interface
export interface ApiRequestConfig extends RequestInit {
  timeout?: number
  retries?: number
  retryDelay?: number
  skipAuth?: boolean
}

// Generic API response interface
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
  status?: number
}

// Error response interface
export interface ApiError {
  message: string
  status: number
  code?: string
  details?: any
}

// Custom error class
export class ApiRequestError extends Error {
  status: number
  code?: string
  details?: any

  constructor(message: string, status: number, code?: string, details?: any) {
    super(message)
    this.name = 'ApiRequestError'
    this.status = status
    this.code = code
    this.details = details
  }
}

// Sleep utility for retry logic
const sleep = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms))

// Request timeout wrapper
const withTimeout = (promise: Promise<Response>, timeoutMs: number): Promise<Response> => {
  const timeout = new Promise<never>((_, reject) =>
    setTimeout(() => reject(new Error('Request timeout')), timeoutMs)
  )
  
  return Promise.race([promise, timeout])
}

// Main API client function
export async function apiClient<T = any>(
  endpoint: string,
  config: ApiRequestConfig = {}
): Promise<T> {
  const {
    timeout = API_CONFIG.TIMEOUT,
    retries = API_CONFIG.RETRY_ATTEMPTS,
    retryDelay = API_CONFIG.RETRY_DELAY,
    skipAuth = false,
    headers: customHeaders,
    ...requestConfig
  } = config

  const url = endpoint.startsWith('http') ? endpoint : `${API_CONFIG.BASE_URL}${endpoint}`

  const defaultHeaders = getDefaultHeaders()
  const headers: HeadersInit = {
    ...defaultHeaders,
    ...customHeaders,
  }

  // Add authentication headers if not skipped
  if (!skipAuth) {
    // Get token from Zustand store
    try {
      const { useAuthStore } = await import('./auth-store')
      const token = useAuthStore.getState().accessToken
      if (token) {
        headers.Authorization = `Bearer ${token}`
      }
    } catch (error) {
      console.warn('Failed to get auth token:', error)
    }
  }

  const requestOptions: RequestInit = {
    mode: 'cors', // Explicitly set CORS mode
    credentials: skipAuth ? 'omit' : 'include', // Include cookies for session-based auth
    headers,
    ...requestConfig,
  }

  let lastError: Error

  // Retry logic
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      console.log(`API Request [${attempt + 1}/${retries + 1}]:`, {
        method: requestOptions.method || 'GET',
        url,
        baseUrl: API_CONFIG.BASE_URL,
        headers: Object.fromEntries(
          Object.entries(headers).filter(([key]) =>
            !key.toLowerCase().includes('authorization')
          )
        )
      })

      const response = await withTimeout(fetch(url, requestOptions), timeout)

      // Log response
      console.log(`API Response [${response.status}]:`, {
        url,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      })

      // Handle different response types
      if (!response.ok) {
        let errorData: any = {}

        try {
          const contentType = response.headers.get('content-type')
          if (contentType?.includes('application/json')) {
            errorData = await response.json()
          } else {
            const textResponse = await response.text()
            errorData = { message: textResponse || `HTTP ${response.status}: ${response.statusText}` }
          }
        } catch (parseError) {
          errorData = { message: `HTTP ${response.status}: ${response.statusText}` }
        }

        // Enhanced error message for common issues
        let enhancedMessage = errorData.message
        if (response.status === 0 || !response.status) {
          enhancedMessage = 'Network error: Unable to connect to server. Check if the backend is running.'
        } else if (response.status === 404) {
          enhancedMessage = `API endpoint not found: ${url}. Check if the backend route exists.`
        } else if (response.status >= 500) {
          enhancedMessage = `Server error (${response.status}): ${errorData.message || response.statusText}`
        }

        throw new ApiRequestError(
          enhancedMessage,
          response.status,
          errorData.code,
          { ...errorData, url, attempt: attempt + 1 }
        )
      }

      // Parse successful response
      const contentType = response.headers.get('content-type')
      if (contentType?.includes('application/json')) {
        const data = await response.json()
        console.log('API Success:', { url, data: Array.isArray(data) ? `Array(${data.length})` : typeof data })
        return data as T
      } else {
        // Handle non-JSON responses (like file downloads)
        return response as unknown as T
      }

    } catch (error) {
      lastError = error as Error

      // Enhanced error logging
      console.error(`API Request Error [Attempt ${attempt + 1}]:`, {
        url,
        error: lastError.message,
        name: lastError.name,
        stack: lastError.stack?.split('\n')[0]
      })

      // Don't retry on certain errors
      if (error instanceof ApiRequestError) {
        // Don't retry on client errors (4xx) except 408, 429
        if (error.status >= 400 && error.status < 500 &&
            error.status !== 408 && error.status !== 429) {
          throw error
        }
      }

      // Don't retry on network errors that are likely permanent
      if (lastError.name === 'TypeError' && lastError.message.includes('fetch')) {
        console.error('Network error detected, not retrying:', lastError.message)
        throw new ApiRequestError(
          'Network error: Unable to connect to the API server. Please check your internet connection and ensure the backend server is running.',
          0,
          'NETWORK_ERROR',
          { originalError: lastError.message, url }
        )
      }

      // If this is the last attempt, throw the error
      if (attempt === retries) {
        console.error(`API Request failed after ${retries + 1} attempts:`, {
          url,
          error: lastError.message,
          stack: lastError.stack
        })
        throw lastError
      }

      // Wait before retrying
      console.warn(`API Request failed, retrying in ${retryDelay * (attempt + 1)}ms...`, {
        attempt: attempt + 1,
        error: lastError.message,
        nextRetryIn: retryDelay * (attempt + 1)
      })

      await sleep(retryDelay * (attempt + 1)) // Exponential backoff
    }
  }

  throw lastError!
}

// Convenience methods for different HTTP verbs
export const api = {
  get: <T = any>(endpoint: string, config?: Omit<ApiRequestConfig, 'method' | 'body'>) =>
    apiClient<T>(endpoint, { ...config, method: 'GET' }),

  post: <T = any>(endpoint: string, data?: any, config?: Omit<ApiRequestConfig, 'method'>) =>
    apiClient<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    }),

  put: <T = any>(endpoint: string, data?: any, config?: Omit<ApiRequestConfig, 'method'>) =>
    apiClient<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    }),

  patch: <T = any>(endpoint: string, data?: any, config?: Omit<ApiRequestConfig, 'method'>) =>
    apiClient<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    }),

  delete: <T = any>(endpoint: string, config?: Omit<ApiRequestConfig, 'method' | 'body'>) =>
    apiClient<T>(endpoint, { ...config, method: 'DELETE' }),

  // Special method for form data (file uploads)
  postForm: <T = any>(endpoint: string, formData: FormData, config?: Omit<ApiRequestConfig, 'method' | 'body'>) =>
    apiClient<T>(endpoint, {
      ...config,
      method: 'POST',
      headers: {
        // Don't set Content-Type for FormData, let browser set it
        ...config?.headers,
      },
      body: formData,
    }),
}

// Health check function
export async function healthCheck(): Promise<boolean> {
  try {
    await api.get('/health', { timeout: 5000, retries: 0 })
    return true
  } catch (error) {
    console.error('Health check failed:', error)
    return false
  }
}

// Export configuration for use in other files
export { API_CONFIG as default }
