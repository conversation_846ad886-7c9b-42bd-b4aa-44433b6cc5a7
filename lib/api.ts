// API service functions for NestJS backend
import { api, type ApiResponse } from './api-config'

// Types for API responses (updated to match backend structure)
export interface Song {
  id: number
  songName: string
  artistName: string
  fileUrl: string
  imageUrl?: string
  genre?: string
  mood?: string
  tempo?: number
  duration: number
  pitch?: number
  tier: "FREE" | "PRO" | "BUSINESS"
  createdAt: string
  updatedAt: string
  createdBy?: number | null
  updatedBy?: number | null
  // Additional frontend properties
  isLiked?: boolean
  waveformData?: number[]
}

export interface SongSearchParams {
  search?: string
  sort?: string
  order?: 'asc' | 'desc'
  limit?: number
  page?: number
  mood?: string
  genre?: string
  artistName?: string
  songName?: string
}

// Backend response structure
export interface SongListResponse {
  items: Song[]
  total: number
}

// For compatibility with existing code
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page?: number
  limit?: number
  totalPages?: number
}

// Note: Using centralized API client from api-config.ts

// Helper functions to convert between backend and frontend formats
function convertBackendToFrontend(backendResponse: SongListResponse): PaginatedResponse<Song> {
  return {
    data: backendResponse.items,
    total: backendResponse.total,
    page: 1, // Backend doesn't return page info, default to 1
    limit: backendResponse.items.length,
    totalPages: 1, // Calculate if needed
  }
}

// Helper to convert tier string to number for backward compatibility
export function getTierNumber(tier: string): 0 | 1 | 2 {
  switch (tier) {
    case 'PRO':
      return 1
    case 'BUSINESS':
      return 2
    case 'FREE':
    default:
      return 0
  }
}

// Helper to convert tier number to string
export function getTierString(tier: 0 | 1 | 2): "FREE" | "PRO" | "BUSINESS" {
  switch (tier) {
    case 1:
      return 'PRO'
    case 2:
      return 'BUSINESS'
    case 0:
    default:
      return 'FREE'
  }
}

// Song API functions
export const songApi = {
  // Search songs with various filters
  async searchSongs(params: SongSearchParams = {}): Promise<SongListResponse> {
    const searchParams = new URLSearchParams()

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, value.toString())
      }
    })

    const endpoint = `/song${searchParams.toString() ? `?${searchParams.toString()}` : ''}`
    const response = await api.get<SongListResponse>(endpoint)
    return response
  },

  // Get song by specific name (as shown in your example)
  async getSongByName(songName: string): Promise<SongListResponse> {
    const endpoint = `/song?songName=${encodeURIComponent(songName)}`
    return api.get<SongListResponse>(endpoint)
  },

  // Get song by ID
  async getSongById(id: number): Promise<Song> {
    return api.get<Song>(`/song/${id}`)
  },

  // Advanced search with multiple parameters
  async advancedSearch(params: {
    search?: string
    genre?: string
    mood?: string
    artistName?: string
    sort?: string
    order?: 'asc' | 'desc'
    page?: number
    limit?: number
  }): Promise<PaginatedResponse<Song>> {
    const backendResponse = await this.searchSongs(params)
    return convertBackendToFrontend(backendResponse)
  },

  // Get songs by genre
  async getSongsByGenre(genre: string, page = 1, limit = 20): Promise<PaginatedResponse<Song>> {
    const backendResponse = await this.searchSongs({ genre, page, limit })
    return convertBackendToFrontend(backendResponse)
  },

  // Get songs by mood
  async getSongsByMood(mood: string, page = 1, limit = 20): Promise<PaginatedResponse<Song>> {
    const backendResponse = await this.searchSongs({ mood, page, limit })
    return convertBackendToFrontend(backendResponse)
  },

  // Get songs by artist
  async getSongsByArtist(artistName: string, page = 1, limit = 20): Promise<PaginatedResponse<Song>> {
    const backendResponse = await this.searchSongs({ artistName, page, limit })
    return convertBackendToFrontend(backendResponse)
  },
}

// Note: Auth API has been moved to lib/auth-api.ts

// Upload API functions
export const uploadApi = {
  async uploadTrack(formData: FormData): Promise<ApiResponse<Song>> {
    return api.postForm<ApiResponse<Song>>('/upload', formData)
  },
}
