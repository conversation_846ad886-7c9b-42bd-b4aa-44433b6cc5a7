// Authentication API service for NestJS backend
import { api, type ApiResponse } from './api-config'

// Types for Auth API
export interface User {
  id: string
  email: string
  name?: string
  role?: string
  tier?: 0 | 1 | 2
  avatar?: string
  createdAt?: string
  updatedAt?: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  password: string
  name?: string
  confirmPassword?: string
}

// Note: Using ApiResponse from api-config.ts

export interface LoginResponse {
  access_token: string
  access_token_expires_at: number
  refresh_token: string
  refresh_token_expires_at: number
  user: {
    id: number
    email: string
    name: string
    roleLst: string[]
    tier: number
    createdAt: string
    updatedAt: string
    tierExpiry: string | null
  }
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export interface ForgotPasswordRequest {
  email: string
}

export interface ResetPasswordRequest {
  token: string
  newPassword: string
  confirmPassword: string
}

export interface UpdateProfileRequest {
  name?: string
  email?: string
  avatar?: string
}

// Note: Using centralized API client from api-config.ts

// Authentication API functions
export const authApi = {
  // Login user
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    return api.post<LoginResponse>('/auth/login', credentials)
  },

  // Register new user
  async register(userData: RegisterRequest): Promise<ApiResponse<LoginResponse>> {
    return api.post<ApiResponse<LoginResponse>>('/auth/register', userData)
  },

  // Logout user
  async logout(): Promise<ApiResponse<null>> {
    return api.post<ApiResponse<null>>('/auth/logout')
  },

  // Get current user info
  async getMe(): Promise<ApiResponse<{ user: User }>> {
    return api.get<ApiResponse<{ user: User }>>('/auth/me')
  },

  // Refresh access token
  async refreshToken(refreshData: RefreshTokenRequest): Promise<ApiResponse<LoginResponse>> {
    return api.post<ApiResponse<LoginResponse>>('/auth/refresh', refreshData)
  },

  // Change password
  async changePassword(passwordData: ChangePasswordRequest): Promise<ApiResponse<null>> {
    return api.post<ApiResponse<null>>('/auth/change-password', passwordData)
  },

  // Forgot password - send reset email
  async forgotPassword(emailData: ForgotPasswordRequest): Promise<ApiResponse<null>> {
    return api.post<ApiResponse<null>>('/auth/forgot-password', emailData)
  },

  // Reset password with token
  async resetPassword(resetData: ResetPasswordRequest): Promise<ApiResponse<null>> {
    return api.post<ApiResponse<null>>('/auth/reset-password', resetData)
  },

  // Update user profile
  async updateProfile(profileData: UpdateProfileRequest): Promise<ApiResponse<{ user: User }>> {
    return api.put<ApiResponse<{ user: User }>>('/auth/profile', profileData)
  },

  // Upload avatar
  async uploadAvatar(avatarFile: File): Promise<ApiResponse<{ user: User }>> {
    const formData = new FormData()
    formData.append('avatar', avatarFile)
    return api.postForm<ApiResponse<{ user: User }>>('/auth/avatar', formData)
  },

  // Delete account
  async deleteAccount(password: string): Promise<ApiResponse<null>> {
    return api.post<ApiResponse<null>>('/auth/delete-account', { password })
  },

  // Verify email
  async verifyEmail(token: string): Promise<ApiResponse<null>> {
    return api.post<ApiResponse<null>>('/auth/verify-email', { token })
  },

  // Resend verification email
  async resendVerification(): Promise<ApiResponse<null>> {
    return api.post<ApiResponse<null>>('/auth/resend-verification')
  },

  // Check if email exists
  async checkEmail(email: string): Promise<ApiResponse<{ exists: boolean }>> {
    return api.get<ApiResponse<{ exists: boolean }>>(`/auth/check-email?email=${encodeURIComponent(email)}`)
  },

  // Get user sessions
  async getSessions(): Promise<ApiResponse<{ sessions: any[] }>> {
    return api.get<ApiResponse<{ sessions: any[] }>>('/auth/sessions')
  },

  // Revoke session
  async revokeSession(sessionId: string): Promise<ApiResponse<null>> {
    return api.delete<ApiResponse<null>>(`/auth/sessions/${sessionId}`)
  },

  // Enable 2FA
  async enable2FA(): Promise<ApiResponse<{ qrCode: string; secret: string }>> {
    return api.post<ApiResponse<{ qrCode: string; secret: string }>>('/auth/2fa/enable')
  },

  // Verify 2FA setup
  async verify2FA(token: string): Promise<ApiResponse<{ backupCodes: string[] }>> {
    return api.post<ApiResponse<{ backupCodes: string[] }>>('/auth/2fa/verify', { token })
  },

  // Disable 2FA
  async disable2FA(password: string): Promise<ApiResponse<null>> {
    return api.post<ApiResponse<null>>('/auth/2fa/disable', { password })
  },

  // Validate 2FA token
  async validate2FA(token: string): Promise<ApiResponse<null>> {
    return api.post<ApiResponse<null>>('/auth/2fa/validate', { token })
  },
}

// Helper functions for auth state management
export const authHelpers = {
  // Check if user is authenticated
  isAuthenticated: (user: User | null): boolean => {
    return user !== null && user.id !== undefined
  },

  // Check if user has specific role
  hasRole: (user: User | null, role: string): boolean => {
    return user?.role === role
  },

  // Check if user has minimum tier
  hasMinimumTier: (user: User | null, minTier: 0 | 1 | 2): boolean => {
    return (user?.tier ?? 0) >= minTier
  },

  // Get user display name
  getDisplayName: (user: User | null): string => {
    return user?.name || user?.email || 'Anonymous'
  },

  // Get tier label
  getTierLabel: (tier?: 0 | 1 | 2): string => {
    switch (tier) {
      case 1:
        return 'Pro'
      case 2:
        return 'Business'
      default:
        return 'Free'
    }
  },
}

// Note: Base URL is managed in api-config.ts
